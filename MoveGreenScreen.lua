-- MoveGreenScreen.lua - 基于createBillboardDrawCell改造的节点
-- 将3D广告牌效果渲染到2D纹理，为后续2D处理做准备
MoveGreenScreen = class(Node)

---@param params.inputTextureName 输入纹理名字【必需】
---@param params.outputTextureName 输出纹理名字【必需】
---@param params.canvasSize 画布大小 {width, height}【必需】
---@param params.cameraParams 相机参数【必需】 {fov: 视场角度, aspect: 宽高比, near: 近裁剪面, far: 远裁剪面}
---@param params.billboardParams 广告牌参数【必需】 {centerX: 中心X坐标, centerY: 中心Y坐标, centerZ: 中心Z坐标, quadWidth: 面片宽度, quadHeight: 面片高度}
function MoveGreenScreen:ctor(params)
    -- 输入参数（所有参数都必需）：
    -- - inputTextureName: 输入纹理名字【必需】
    -- - outputTextureName: 输出纹理名字【必需】
    -- - canvasSize: 画布大小 {width, height}【必需】
    -- - cameraParams: 相机参数【必需】
    --   {
    --     fov: 视场角，单位度 (number)
    --     aspect: 宽高比 (number)
    --     near: 近裁剪面距离 (number)
    --     far: 远裁剪面距离 (number)
    --   }
    -- - billboardParams: 广告牌参数【必需】
    --   {
    --     centerX: 广告牌中心X坐标 (number)
    --     centerY: 广告牌中心Y坐标 (number)
    --     centerZ: 广告牌中心Z坐标 (number)
    --     quadWidth: 广告牌面片宽度 (number)
    --     quadHeight: 广告牌面片高度 (number)
    --   }
    -- 所有参数都必须明确提供，不使用默认值
    assert(params.inputTextureName, "MoveGreenScreen: inputTextureName is required")
    assert(params.outputTextureName, "MoveGreenScreen: outputTextureName is required")
    assert(params.canvasSize, "MoveGreenScreen: canvasSize is required")
    assert(params.cameraParams, "MoveGreenScreen: cameraParams is required")
    assert(params.billboardParams, "MoveGreenScreen: billboardParams is required")

    -- 参数
    self.inputTextureName = params.inputTextureName
    self.outputTextureName = params.outputTextureName
    self.canvasSize = params.canvasSize

    -- 相机参数
    self.cameraParams = params.cameraParams

    -- 广告牌参数
    self.billboardParams = params.billboardParams

    -- 创建输出纹理节点
    self.outputNode = RenderTargetNode.createWithSize(self.outputTextureName, self.canvasSize[1], self.canvasSize[2])
    self:addChild(self.outputNode)

    -- 创建广告牌DrawCell
    self.drawCell = self:createBillboardDrawCell()
    self.outputNode:addChild(self.drawCell)
end

function MoveGreenScreen:createBillboardDrawCell()
    local drawCell = BKCommonDrawCell.new()

    -- 设置前景面片专用着色器
    drawCell:setProgramUsingShaderFile(gResourceRootPath .. "/resources/shaders/foreground",
        gResourceRootPath .. "/resources/shaders/foreground")

    -- 获取广告牌前景视频面片数据（复用原有函数）
    local fg_positionDataArray, fg_colorDataArray, _, fg_texCoordDataArray = self:createBillboardVideoData()

    if #fg_positionDataArray == 0 then
        return drawCell
    end

    -- 设置顶点属性
    local totalVertexCount = #fg_positionDataArray / 3
    drawCell:addVertexAttribute("a_position", fg_positionDataArray, totalVertexCount, BKDataType.BKFloat3, false)
    drawCell:addVertexAttribute("a_color", fg_colorDataArray, totalVertexCount, BKDataType.BKFloat4, false)
    drawCell:addVertexAttribute("a_texCoord", fg_texCoordDataArray, totalVertexCount, BKDataType.BKFloat2, false)

    -- 设置矩阵uniforms
    local modelMatrix = Matrix.new()
    modelMatrix:setIdentity()
    local viewMatrix = Matrix.new()
    viewMatrix:setIdentity()
    local projectionMatrix = Matrix.new()
    Matrix.createPerspective(self.cameraParams.fov, self.cameraParams.aspect, self.cameraParams.near,
        self.cameraParams.far, projectionMatrix)

    drawCell:addVertexUniform("modelMatrix", modelMatrix:mDataArray(), 1, BKDataType.BKMat4)
    drawCell:addVertexUniform("viewMatrix", viewMatrix:mDataArray(), 1, BKDataType.BKMat4)
    drawCell:addVertexUniform("projectionMatrix", projectionMatrix:mDataArray(), 1, BKDataType.BKMat4)

    -- 添加广告牌变换矩阵uniform（初始化为单位矩阵）
    local identityMatrix = Matrix.new()
    identityMatrix:setIdentity()
    drawCell:addVertexUniform("billboardMatrix", identityMatrix:mDataArray(), 1, BKDataType.BKMat4)

    -- 设置索引数据
    -- local fg_drawingIndicesDataArray = {0, 1, 2, 0, 2, 3}
    -- drawCell:setDrawingIndicesData(fg_drawingIndicesDataArray, 6)
    drawCell:setDrawingMode(BKDrawingMode.BKDrawingModeTriangleStrip)

    -- 添加前景视频纹理
    drawCell:addInputTextureWithName("FG_TEX", self.inputTextureName, false, false)

    -- 设置渲染状态
    drawCell:setLoadAction(BKLoadAction.BKLoadActionClear)
    -- drawCell:enableDefaultBlend()
    -- drawCell:setDepthTestEnable(true)
    -- drawCell:setDepthCompareOp(BKDepthCompareOp.BKDepthCompareOpLessOrEqual)

    return drawCell
end

function MoveGreenScreen:createBillboardVideoData()
    -- 复用原有的createBillboardVideoData函数逻辑
    local fg_positionDataArray = {}
    local fg_colorDataArray = {}
    local fg_objectTypeDataArray = {}
    local fg_texCoordDataArray = {}

    -- 广告牌模式：顶点位置使用本地坐标系（-0.5到0.5范围）
    local localPositions = {{-0.5, -0.5, 0.0}, {0.5, -0.5, 0.0}, {-0.5, 0.5, 0.0}, {0.5, 0.5, 0.0}}
    local texCoords = {{0.0, 0.0}, {1.0, 0.0}, {0.0, 1.0}, {1.0, 1.0}}

    for i = 1, 4 do
        local pos = localPositions[i]
        local tex = texCoords[i]

        fg_positionDataArray[#fg_positionDataArray + 1] = pos[1]
        fg_positionDataArray[#fg_positionDataArray + 1] = pos[2]
        fg_positionDataArray[#fg_positionDataArray + 1] = pos[3]

        fg_colorDataArray[#fg_colorDataArray + 1] = 1.0
        fg_colorDataArray[#fg_colorDataArray + 1] = 1.0
        fg_colorDataArray[#fg_colorDataArray + 1] = 1.0
        fg_colorDataArray[#fg_colorDataArray + 1] = 1.0

        fg_objectTypeDataArray[#fg_objectTypeDataArray + 1] = 4.0

        fg_texCoordDataArray[#fg_texCoordDataArray + 1] = tex[1]
        fg_texCoordDataArray[#fg_texCoordDataArray + 1] = tex[2]
    end

    return fg_positionDataArray, fg_colorDataArray, fg_objectTypeDataArray, fg_texCoordDataArray
end

-- 更新矩阵参数
function MoveGreenScreen:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)
    if self.drawCell then
        local modelMatrix = Matrix.new()
        modelMatrix:setIdentity()

        self.drawCell:updateUniformData("modelMatrix", modelMatrix:mDataArray())
        self.drawCell:updateUniformData("viewMatrix", viewMatrix:mDataArray())
        self.drawCell:updateUniformData("projectionMatrix", projectionMatrix:mDataArray())

        -- 更新广告牌变换矩阵（如果提供的话）
        if billboardMatrix then
            self.drawCell:updateUniformData("billboardMatrix", billboardMatrix:mDataArray())
        end
    end
end

-- 更新广告牌参数
function MoveGreenScreen:updateBillboardParams(centerX, centerY, centerZ, quadWidth, quadHeight)
    self.billboardParams.centerX = centerX
    self.billboardParams.centerY = centerY
    self.billboardParams.centerZ = centerZ
    self.billboardParams.quadWidth = quadWidth
    self.billboardParams.quadHeight = quadHeight
end

-- 更新相机参数
function MoveGreenScreen:updateCameraParams(fov, aspect, near, far)
    self.cameraParams.fov = fov
    self.cameraParams.aspect = aspect
    self.cameraParams.near = near
    self.cameraParams.far = far
end

-- 获取输出节点（用于添加到场景）
function MoveGreenScreen:getOutputNode()
    return self.outputNode
end

return MoveGreenScreen
