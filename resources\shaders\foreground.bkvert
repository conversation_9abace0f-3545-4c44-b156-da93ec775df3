#version 310 es

layout(location=0) in vec3 a_position;
layout(location=1) in vec4 a_color;
layout(location=2) in vec2 a_texCoord;

layout(binding=0) uniform VertUniforms {
    mat4 modelMatrix;
    mat4 viewMatrix;
    mat4 projectionMatrix;
    mat4 billboardMatrix;  // 广告牌变换矩阵（CPU端预计算，包含旋转、缩放、平移）
};

layout(location=0) out vec4 color;
layout(location=1) out vec2 texCoord;

void main()
{
    // 广告牌实现：使用CPU端预计算的变换矩阵
    // a_position.xy 表示在广告牌本地坐标系中的偏移量（-0.5到0.5范围）

    // 应用广告牌变换矩阵（包含旋转、缩放、平移）
    vec4 worldPosition = billboardMatrix * vec4(a_position, 1.0);

    // 应用视图和投影变换
    vec4 viewPosition = viewMatrix * worldPosition;
    gl_Position = projectionMatrix * viewPosition;

    color = a_color;
    texCoord = a_texCoord;
}
