-- ProjectionDrawCell.lua - 投影面片DrawCell实现
-- 独立的DrawCell，用于在前景面片底部绘制投影面片
ProjectionDrawCell = class(Node)
local function calculateDepthCompare(rotationPitch, rotationYaw)
    local pitch = math.rad(rotationPitch)
    local yaw = math.rad(rotationYaw)
    local cosPitch = math.cos(pitch)
    local sinPitch = math.sin(pitch)
    local cosYaw = math.cos(yaw)
    local sinYaw = math.sin(yaw)
    local direction = Vector3.new(sinYaw * cosPitch, -sinPitch, cosYaw * cosPitch)
    local dotProduct = direction:dot(Vector3.new(0, 1, 0))
    -- 返回 -1 或 1：向下投影返回 -1，向上投影返回 1
    return dotProduct >= 0 and 1 or -1
end
function ProjectionDrawCell:ctor(params)
    -- 输入参数（所有参数都必需）：
    -- - inputTextureName: 输入纹理名称（背景纹理）【必需】
    -- - maskTextureName: 掩模纹理名称（绿幕抠像的alpha通道）【必需】
    -- - outputTextureName: 输出纹理名称【必需】
    -- - greenScreenTexSize: 绿幕纹理尺寸 {width, height}【必需】
    -- - canvasSize: 画布尺寸 {width, height}【必需】
    -- - projectionParams: 投影参数配置【必需】
    --   {
    --     enabled: 是否启用投影 (boolean)
    --     directionPitch: 投影方向俯仰角，单位度 (number)
    --     directionYaw: 投影方向偏航角，单位度 (number)
    --     intensity: 投影强度，控制透明度 (number, 0.0-1.0)
    --     maxDistance: 最大投影距离 (number)
    --     color: 投影颜色 RGB {r, g, b} (table)
    --     shadowGroundHeight: 阴影离地面高度，用于微调阴影垂直位置 (number)
    --     depthCompare: 阴影深度比较 (number)
    --     depthRedundancy: 阴影深度冗余 (number)
    --     rescale: 阴影模糊的缩放比例 (number)
    --   }
    -- - groundParams: 地面参数配置【必需】
    --   {
    --     positionX: 地面网格X位置 (number)
    --     positionY: 地面网格Y位置 (number)
    --     positionZ: 地面网格Z位置 (number)
    --     rotationX: 地面网格X轴旋转角度，单位度 (number)
    --     rotationY: 地面网格Y轴旋转角度，单位度 (number)
    --     rotationZ: 地面网格Z轴旋转角度，单位度 (number)
    --   }

    -- 所有参数都必须明确提供，不使用默认值
    assert(params.inputTextureName, "ProjectionDrawCell: inputTextureName is required")
    assert(params.maskTextureName, "ProjectionDrawCell: maskTextureName is required")
    assert(params.outputTextureName, "ProjectionDrawCell: outputTextureName is required")

    self.inputTextureName = params.inputTextureName
    self.maskTextureName = params.maskTextureName
    self.outputTextureName = params.outputTextureName

    -- 所有参数都必须明确提供，不使用默认值
    assert(params.greenScreenTexSize, "ProjectionDrawCell: greenScreenTexSize is required")
    assert(params.canvasSize, "ProjectionDrawCell: canvasSize is required")
    assert(params.projectionParams, "ProjectionDrawCell: projectionParams is required")
    assert(params.groundParams, "ProjectionDrawCell: groundParams is required")

    self.greenScreenTexSize = params.greenScreenTexSize
    self.canvasSize = params.canvasSize

    -- 投影参数配置
    self.projectionParams = params.projectionParams
    self.projectionParams.depthCompare = calculateDepthCompare(self.projectionParams.directionPitch,
        self.projectionParams.directionYaw)

    -- 地面参数配置
    self.groundParams = params.groundParams

    -- 创建输出纹理节点
    self.outputNode = RenderTargetNode.createWithSize(self.outputTextureName, self.canvasSize[1], self.canvasSize[2])
    self.outputNode:setRenderTargetInitWithCopyInput()
    self:addChild(self.outputNode)

    -- 创建投影DrawCell
    self:createProjectionDrawCell()
end

-- 计算地面plane参数
function ProjectionDrawCell:calculateGroundPlaneParams()
    -- 使用传入的地面参数
    local groundPos = Vector3.new(self.groundParams.positionX, self.groundParams.positionY, self.groundParams.positionZ)

    -- 计算地面的法向量（考虑旋转）
    local rotMatrixX = Matrix.new()
    local rotMatrixY = Matrix.new()
    local rotMatrixZ = Matrix.new()
    local finalRotMatrix = Matrix.new()

    Matrix.createRotationX(math.rad(self.groundParams.rotationX), rotMatrixX)
    Matrix.createRotationY(math.rad(self.groundParams.rotationY), rotMatrixY)
    Matrix.createRotationZ(math.rad(self.groundParams.rotationZ), rotMatrixZ)

    finalRotMatrix:set(rotMatrixZ)
    finalRotMatrix:multiply(rotMatrixY)
    finalRotMatrix:multiply(rotMatrixX)

    -- 计算旋转后的法向量（原始法向量为(0,1,0)）
    local originalNormal = Vector3.new(0.0, 1.0, 0.0)
    local rotatedNormal = Vector3.new()
    finalRotMatrix:transformVector(originalNormal, rotatedNormal)

    return {
        point = {groundPos:x(), groundPos:y(), groundPos:z()},
        normal = {rotatedNormal:x(), rotatedNormal:y(), rotatedNormal:z()}
    }
end

-- 创建投影DrawCell
function ProjectionDrawCell:createProjectionDrawCell()
    -- 阴影模糊
    -- 阴影模糊
    local shadowBlurNodeH = RenderTargetNode.createWithSize("SHADOW_BLUR_TEXTURE_H",
        self.greenScreenTexSize[1] / self.projectionParams.rescale, self.greenScreenTexSize[2] /
            self.projectionParams.rescale)
    self:addChild(shadowBlurNodeH)
    local shadowBlurDCH = BKCommonDrawCell.new()
    shadowBlurDCH:setProgramUsingShaderFile(gResourceRootPath .. '/resources/shaders/shadow_blur',
        gResourceRootPath .. '/resources/shaders/shadow_blur')
    shadowBlurDCH:addVertexAttribute("a_position", {-1.0, -1.0, 1.0, -1.0, -1.0, 1.0, 1.0, 1.0}, 4, BKDataType.BKFloat2,
        false)
    shadowBlurDCH:addVertexAttribute("a_texCoord", {0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0}, 4, BKDataType.BKFloat2,
        false)
    shadowBlurDCH:addInputTextureWithName("inputImageTexture", self.maskTextureName)
    shadowBlurDCH:addFragmentUniform("baseTexWidth", {self.greenScreenTexSize[1] / self.projectionParams.rescale}, 1,
        BKDataType.BKFloat)
    shadowBlurDCH:addFragmentUniform("baseTexHeight", {self.greenScreenTexSize[2] / self.projectionParams.rescale}, 1,
        BKDataType.BKFloat)
    shadowBlurDCH:addFragmentUniform("direction", {1, 0}, 1, BKDataType.BKFloat2)
    shadowBlurDCH:setLoadAction(BKLoadAction.BKLoadActionClear)
    shadowBlurDCH:setDrawingMode(BKDrawingMode.BKDrawingModeTriangleStrip)
    shadowBlurNodeH:addChild(shadowBlurDCH)

    local shadowBlurNodeV = RenderTargetNode.createWithSize("SHADOW_BLUR_TEXTURE_V",
        self.greenScreenTexSize[1] / self.projectionParams.rescale, self.greenScreenTexSize[2] /
            self.projectionParams.rescale)
    self:addChild(shadowBlurNodeV)
    local shadowBlurDCV = BKCommonDrawCell.new()
    shadowBlurDCV:setProgramUsingShaderFile(gResourceRootPath .. '/resources/shaders/shadow_blur',
        gResourceRootPath .. '/resources/shaders/shadow_blur')
    shadowBlurDCV:addVertexAttribute("a_position", {-1.0, -1.0, 1.0, -1.0, -1.0, 1.0, 1.0, 1.0}, 4, BKDataType.BKFloat2,
        false)
    shadowBlurDCV:addVertexAttribute("a_texCoord", {0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0}, 4, BKDataType.BKFloat2,
        false)
    shadowBlurDCV:addInputTextureWithName("inputImageTexture", "SHADOW_BLUR_TEXTURE_H")
    shadowBlurDCV:addFragmentUniform("baseTexWidth", {self.greenScreenTexSize[1] / self.projectionParams.rescale}, 1,
        BKDataType.BKFloat)
    shadowBlurDCV:addFragmentUniform("baseTexHeight", {self.greenScreenTexSize[2] / self.projectionParams.rescale}, 1,
        BKDataType.BKFloat)
    shadowBlurDCV:addFragmentUniform("direction", {0, 1}, 1, BKDataType.BKFloat2)
    shadowBlurDCV:setLoadAction(BKLoadAction.BKLoadActionClear)
    shadowBlurDCV:setDrawingMode(BKDrawingMode.BKDrawingModeTriangleStrip)
    shadowBlurNodeV:addChild(shadowBlurDCV)

    self.drawCell = BKCommonDrawCell.new()

    -- 设置投影专用着色器
    self.drawCell:setProgramUsingShaderFile(gResourceRootPath .. "/resources/shaders/projection",
        gResourceRootPath .. "/resources/shaders/projection")

    -- 使用与前景面片相同的本地坐标系顶点数据
    local localPositions = {-0.5, -0.5, 0.0, 0.5, -0.5, 0.0, -0.5, 0.5, 0.0, 0.5, 0.5, 0.0}
    -- 使用与前景面片相同的纹理坐标
    local texCoords = {0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0}

    self.drawCell:addVertexAttribute("a_position", localPositions, 4, BKDataType.BKFloat3, false)
    self.drawCell:addVertexAttribute("a_texCoord", texCoords, 4, BKDataType.BKFloat2, false)

    -- 设置矩阵uniforms（初始化）
    local identityMatrix = Matrix.new()
    identityMatrix:setIdentity()

    self.drawCell:addVertexUniform("modelMatrix", identityMatrix:mDataArray(), 1, BKDataType.BKMat4)
    self.drawCell:addVertexUniform("viewMatrix", identityMatrix:mDataArray(), 1, BKDataType.BKMat4)
    self.drawCell:addVertexUniform("projectionMatrix", identityMatrix:mDataArray(), 1, BKDataType.BKMat4)
    self.drawCell:addVertexUniform("billboardMatrix", identityMatrix:mDataArray(), 1, BKDataType.BKMat4)

    -- 设置地面plane参数
    local groundParams = self:calculateGroundPlaneParams()
    self.drawCell:addVertexUniform("groundPlanePoint", groundParams.point, 1, BKDataType.BKFloat3)
    self.drawCell:addVertexUniform("groundPlaneNormal", groundParams.normal, 1, BKDataType.BKFloat3)

    -- 设置投影参数
    local projRotation = {math.rad(self.projectionParams.directionPitch), math.rad(self.projectionParams.directionYaw)}
    self.drawCell:addVertexUniform("projectionRotation", projRotation, 1, BKDataType.BKFloat2)
    self.drawCell:addVertexUniform("projectionIntensity", {self.projectionParams.intensity}, 1, BKDataType.BKFloat)
    self.drawCell:addVertexUniform("projectionDistance", {self.projectionParams.maxDistance}, 1, BKDataType.BKFloat)
    self.drawCell:addVertexUniform("shadowGroundHeight", {self.projectionParams.shadowGroundHeight}, 1,
        BKDataType.BKFloat)

    -- 添加投影颜色uniform（fragment shader使用）
    self.drawCell:addFragmentUniform("projectionColor", self.projectionParams.color, 1, BKDataType.BKFloat3)
    self.drawCell:addFragmentUniform("depthCompare", {self.projectionParams.depthCompare}, 1, BKDataType.BKFloat)
    self.drawCell:addFragmentUniform("depthRedundancy", {self.projectionParams.depthRedundancy}, 1, BKDataType.BKFloat)

    -- 添加背景纹理输入
    self.drawCell:addInputTextureWithName("BG_TEX", self.inputTextureName, false, false)

    -- 添加绿幕抠像掩模纹理输入
    self.drawCell:addInputTextureWithName("MASK_TEX", "SHADOW_BLUR_TEXTURE_V", false, false)

    -- 设置索引
    self.drawCell:setDrawingMode(BKDrawingMode.BKDrawingModeTriangleStrip)

    -- 设置渲染状态
    self.drawCell:setLoadAction(BKLoadAction.BKLoadActionNothing)
    self.outputNode:addChild(self.drawCell)
end

-- 更新矩阵参数（与前景面片同步）
function ProjectionDrawCell:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)
    if self.drawCell then
        local modelMatrix = Matrix.new()
        modelMatrix:setIdentity()

        self.drawCell:updateUniformData("modelMatrix", modelMatrix:mDataArray())
        self.drawCell:updateUniformData("viewMatrix", viewMatrix:mDataArray())
        self.drawCell:updateUniformData("projectionMatrix", projectionMatrix:mDataArray())

        -- 关键：使用与前景面片相同的billboardMatrix
        if billboardMatrix then
            self.drawCell:updateUniformData("billboardMatrix", billboardMatrix:mDataArray())
        end
    end
end

-- 更新投影参数
function ProjectionDrawCell:updateProjectionParams(params)
    if self.drawCell and params then
        -- 更新投影方向
        if params.directionPitch or params.directionYaw then
            local depthCompare = calculateDepthCompare(params.directionPitch, params.directionYaw)
            local pitch = math.rad(params.directionPitch or self.projectionParams.directionPitch)
            local yaw = math.rad(params.directionYaw or self.projectionParams.directionYaw)
            self.drawCell:updateUniformData("projectionRotation", {pitch, yaw})
            self.drawCell:updateUniformData("depthCompare", {depthCompare})
        end

        -- 更新其他参数
        if params.intensity then
            self.drawCell:updateUniformData("projectionIntensity", {params.intensity})
        end
        if params.maxDistance then
            self.drawCell:updateUniformData("projectionDistance", {params.maxDistance})
        end
        if params.shadowGroundHeight then
            self.drawCell:updateUniformData("shadowGroundHeight", {params.shadowGroundHeight})
        end
        if params.color then
            self.drawCell:updateUniformData("projectionColor", params.color)
        end
        if params.depthRedundancy then
            self.drawCell:updateUniformData("depthRedundancy", {params.depthRedundancy})
        end

        -- 更新本地参数缓存
        for key, value in pairs(params) do
            self.projectionParams[key] = value
        end
    end
end

-- 更新地面参数（当地面参数变化时调用）
function ProjectionDrawCell:updateGroundParams(groundParams)
    if self.drawCell and groundParams then
        -- 重新计算地面plane参数
        local planeParams = self:calculateGroundPlaneParams()
        self.drawCell:updateUniformData("groundPlanePoint", planeParams.point)
        self.drawCell:updateUniformData("groundPlaneNormal", planeParams.normal)
    end
end

-- 设置是否启用投影
function ProjectionDrawCell:setEnabled(enabled)
    if self.outputNode then
        self.outputNode:setVisible(enabled)
    end
    self.projectionParams.enabled = enabled
end

-- 获取输出纹理名称
function ProjectionDrawCell:getOutputTextureName()
    return self.outputTextureName
end
