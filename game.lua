-- ============================================================================
-- 绿幕抠像功能集成
-- ============================================================================
-- 导入绿幕功能节点
local gspath = 'D:/HuyaProject2/greenscreenmatting'
package.path = package.path .. ";" .. gspath .. "/?.lua"

-- 导入必要的绿幕节点
require('effect.Logger')
require('effect.GreenScreenMatting')
require('effect.BackgroundBlend')
require('effect.DetailBlend')
require('MoveGreenScreen')
require('ProjectionDrawCell')

native_print("绿幕抠像节点已导入")

-- ============================================================================
-- 纹理名称全局配置
-- ============================================================================
-- 统一管理所有节点使用的纹理名称，避免硬编码散落在各个地方
local TEXTURE_NAMES = {
    -- 视频相关纹理
    FG_VIDEO_RAW = "FG_VIDEO_RAW", -- 原始前景视频纹理
    FG_TEX = "FG_TEX", -- 绿幕抠像后的前景纹理
    BG_TEXTURE = "BG_TEXTURE", -- 背景视频纹理

    -- 绿幕抠像相关纹理
    MOVE_GREENSCREEN_TEXTURE = "MOVE_GREENSCREEN_TEXTURE", -- 3D变换后的前景纹理
    MATTING_DETAIL_MASK = "mattingOutputDetailMaskTex", -- 绿幕抠像细节掩模
    MATTING_ALPHA_MASK = "mattingOutputAlphaMaskTex", -- 绿幕抠像透明度掩模

    -- 融合相关纹理
    BG_BLEND_OUTPUT = "bgBlendOutputTex", -- 背景融合输出纹理
    DETAIL_BLEND_OUTPUT = "detailBlendOutputTex", -- 细节融合输出纹理

    -- 投影相关纹理
    PROJECTION_BG_TEXTURE = "PROJECTION_BG_TEXTURE", -- 投影背景纹理
    SHADOW_BLUR_H = "SHADOW_BLUR_TEXTURE_H", -- 水平阴影模糊纹理
    SHADOW_BLUR_V = "SHADOW_BLUR_TEXTURE_V", -- 垂直阴影模糊纹理

    -- 着色器输入纹理名称
    SHADER_INPUT_IMAGE = "inputImageTexture", -- 着色器通用输入纹理名
    SHADER_FG_TEX = "FG_TEX", -- 着色器前景纹理名
    SHADER_BG_TEX = "BG_TEX", -- 着色器背景纹理名
    SHADER_MASK_TEX = "MASK_TEX" -- 着色器掩模纹理名
}

-- ============================================================================
-- 用户可调节参数配置区域
-- ============================================================================

-- ===== 文件路径配置 =====
-- local FG_VIDEO_FILE_PATH = "/pydata/古典美女.mp4"
-- local FG_VIDEO_FILE_PATH = "/pydata/古典美女-正方形.mp4"
-- local FG_VIDEO_FILE_PATH = "/pydata/古典跳舞-正方形-静态.mp4"
local FG_VIDEO_FILE_PATH = "/pydata/fix跳舞-高清.mp4"

-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/garden_left"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/garden_left_down"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/garden_pull_out"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/garden_right"

-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/loveheart_left"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/loveheart_left_down"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/loveheart_right"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/new_len/loveheart_up"
local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/cyber_len2"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/bridge"
-- local PROJECT_DIR = "D:/HuyaProject2/slam_greenscreen/data/girl_river1"

local OUTPUT_CAMERA_FILE_PATH = PROJECT_DIR .. "/output_cameras.json"
local USER_CONFIG_FILE_PATH = PROJECT_DIR .. "/user_config.json"
local user_config = rapidjson.load(USER_CONFIG_FILE_PATH)

-- ===== 显示控制开关 =====
local isPauseFirstFrame = true -- 是否暂停第一帧视频

-- ===== 绿幕抠像参数配置 =====
local greenscreen_params = {
    -- 亮度调整
    gamma = 1.1,
    contrast = 0.55,
    brightness = 0.5,
    -- 基础抠像参数
    key_color = "#2957d4", -- 绿幕颜色
    similarity = 0.0, -- 相似度 (0.0-1.0)
    edge_indent = 1.2, -- 边缘缩进 (0.0-1.0)
    radius = 0.6, -- 头发范围 (0.0-1.0)
    spill_suppression = 0.5, -- 溢色抑制 (0.0-1.0)
    key_gain = 0.35, -- 透明度粗调 (0.0-1.0)
    key_lift = 0.30, -- 透明度细调 (0.0-1.0)
    fxaa_smooth = 0.5, -- FXAA边缘平滑度 (0.0-1.0)
    -- 溢色抑制详细参数
    spill_sup_flag = 5.0,
    spill_sup_scale_r = 0.496184,
    spill_sup_scale_g = 0.4993695,
    spill_sup_scale_b = 0.45,
    spill_sup_red_w = 0.85,
    spill_sup_mix = 0.52
}

-- ============================================================================
-- Config参数配置区域
-- ============================================================================
local BG_VIDEO_FILE_PATH = PROJECT_DIR .. "/../" .. user_config.video

-- ===== 用户可调整的2D风格控制参数（将根据配置来调整） =====
local FG_USER_ADJUST_X = user_config.fg_adjust.x -- 用户X轴偏移（屏幕宽度为单位，1.0=一个屏幕宽度）
local FG_USER_ADJUST_Y = user_config.fg_adjust.y -- 用户Y轴偏移（屏幕高度为单位，1.0=一个屏幕高度）
local FG_USER_ADJUST_SCALE = user_config.fg_adjust.scale -- 用户缩放系数（以面片中心为原点）

-- ===== 投影面片参数（将根据配置来调整） =====
local PROJECTION_ENABLED = true -- 是否启用投影
local PROJECTION_DIRECTION_PITCH = user_config.projection.rotation.pitch -- 投影方向俯仰角（度）
local PROJECTION_DIRECTION_YAW = user_config.projection.rotation.yaw -- 投影方向偏航角（度）
local PROJECTION_INTENSITY = user_config.projection.intensity -- 投影强度（控制透明度）
local PROJECTION_MAX_DISTANCE = user_config.projection.max_distance or 25.0 -- 最大投影距离
local PROJECTION_COLOR = user_config.projection.color -- 投影颜色（RGB）
local PROJECTION_SHADOW_GROUND_HEIGHT = user_config.projection.ground_height -- 阴影离地面高度（用于微调阴影垂直位置）
local PROJECTION_DEPTH_COMPARE = 0 --user_config.projection.depth_compare -- 阴影深度对比（按道理应该是用rotation计算出来的）
local PROJECTION_DEPTH_REDUNDANCY = user_config.projection.depth_redundancy -- 阴影深度冗余
local PROJECTION_RESCALE = 4.0 -- 阴影模糊的缩放比例（一般不用调整）

-- ===== 背景融合和细节融合参数（将根据配置来调整） =====
local background_blend_intensity = user_config.bg_blend_intensity -- 背景融合强度 (0.0-2.0)
local detail_blend_strength = user_config.detail_blend_intensity -- 细节融合强度 (0.0-2.0)

-- ============================================================================
-- 其他参数（通常不需要用户调整）
-- ============================================================================
-- ===== 地面网格参数（从JSON文件中读取，可手动调整） =====
local GROUND_POSITION_X = 0.0 -- 地面网格X位置
local GROUND_POSITION_Y = 0.0 -- 地面网格Y位置
local GROUND_POSITION_Z = 0.0 -- 地面网格Z位置
local GROUND_ROTATION_X = 0.0 -- 地面网格X轴旋转角度（度数）
local GROUND_ROTATION_Y = 0.0 -- 地面网格Y轴旋转角度（度数）
local GROUND_ROTATION_Z = 0.0 -- 地面网格Z轴旋转角度（度数）

-- ===== 前景视频面片参数（从JSON文件中读取，可手动调整） =====
local FG_VIDEO_QUAD_SIZE = 1.0 -- 前景视频面片基础大小
local FG_VIDEO_QUAD_POSITION_X = 0.0 -- 前景视频面片X位置
local FG_VIDEO_QUAD_POSITION_Y = 0.0 -- 前景视频面片Y位置
local FG_VIDEO_QUAD_POSITION_Z = 0.0 -- 前景视频面片Z位置
local FG_VIDEO_QUAD_ROTATION_X = 0.0 -- 前景视频面片X轴旋转角度（度数）
local FG_VIDEO_QUAD_ROTATION_Y = 0.0 -- 前景视频面片Y轴旋转角度（度数）
local FG_VIDEO_QUAD_ROTATION_Z = 0.0 -- 前景视频面片Z轴旋转角度（度数）

-- ============================================================================
-- 系统内部参数（通常不需要用户调整）
-- ============================================================================
-- ===== 几何尺寸参数 =====
local GRID_SIZE = 100.0 -- 地面网格大小

-- ===== 默认场景缩放和视角控制（不要修改） =====
local scaleFactorFrom3DE = 1 -- 3DE数据缩放因子，将厘米转换为场景单位（厘米->场景单位）
local currentCameraFrame = 1 -- 当前使用的相机帧索引

-- ===== 默认相机和视图参数 =====
local DEFAULT_FIELD_OF_VIEW = 30.0 -- 默认视野角度
local DEFAULT_ASPECT_RATIO = 1920.0 / 1080.0 -- 默认屏幕宽高比
local NEAR_PLANE = 0.01 -- 近裁剪面
local FAR_PLANE = 10000.0 -- 远裁剪面

-- ===== 画布尺寸参数（使用初始化参数确定） =====
local CANVAS_Width = 1920 -- 默认宽度
local CANVAS_Height = 1080 -- 默认高度

-- 动态相机参数（从配置文件中读取）
local CAMERA_FOV = DEFAULT_FIELD_OF_VIEW
local CAMERA_ASPECT_RATIO = DEFAULT_ASPECT_RATIO

-- 前景视频比例信息（从视频中提取）
local FG_VIDEO_ASPECT_RATIO = 1920.0 / 1080.0 -- 前景视频宽高比
local FG_VIDEO_WIDTH = 1920 -- 前景视频宽度（从视频中提取）
local FG_VIDEO_HEIGHT = 1080 -- 前景视频高度（从视频中提取）

-- 前景面片屏幕对齐参数（自动计算）
local FG_SCREEN_ALIGN_X = 0.0 -- 前景面片屏幕对齐X位置偏移
local FG_SCREEN_ALIGN_Y = 0.0 -- 前景面片屏幕对齐Y位置偏移
local FG_SCREEN_ALIGN_SCALE = 1.0 -- 前景面片屏幕对齐缩放系数

-- ============================================================================
-- 全局变量
-- ============================================================================
local video_config = nil
local BackgroundVideoMeida = nil

-- 绿幕抠像相关变量
local effect_GreenScreenMatting = nil
local effect_BackgroundBlend = nil
local effect_DetailBlend = nil
local camera_move_gs = nil
local projectionDrawCell = nil

-- ============================================================================
-- 预计算矩阵缓存
-- ============================================================================
local precomputedMatrices = {
    billboardMatrices = {}, -- 每帧的广告牌矩阵
    billboardParams = {}, -- 每帧的广告牌参数
    cameraPositions = {}, -- 每帧的相机位置
    totalFrames = 0 -- 总帧数
}

-- ============================================================================
-- 绿幕抠像功能函数
-- ============================================================================

-- 计算相机FOV和纵横比
local function calculateCameraParams()
    if not video_config or not video_config.camera or not video_config.camera.camera_info then
        return
    end

    local camera_info = video_config.camera.camera_info
    local focal_length = camera_info.focal_length
    local render_width = camera_info.render_width
    local render_height = camera_info.render_height
    local aperture_width = camera_info.aperture_width

    if focal_length and render_width and render_height and aperture_width then
        -- 计算纵横比
        CAMERA_ASPECT_RATIO = render_width / render_height

        -- 从焦距和光圈宽度计算水平视野角度（FOV_x）
        local fov_x_radians = 2 * math.atan(aperture_width / (2 * focal_length))

        -- 将水平FOV转换为垂直FOV
        local fov_y_radians = 2 * math.atan(math.tan(fov_x_radians / 2) / CAMERA_ASPECT_RATIO)
        CAMERA_FOV = math.deg(fov_y_radians)

        native_print("相机参数计算完成: FOV=" .. CAMERA_FOV .. "度, 纵横比=" .. CAMERA_ASPECT_RATIO)
    else
        native_print("相机参数不完整，使用默认值")
        CAMERA_FOV = DEFAULT_FIELD_OF_VIEW
        CAMERA_ASPECT_RATIO = DEFAULT_ASPECT_RATIO
    end
end

-- 加载合并的数据文件（包含相机、点和视频数据）
local function loadCombinedData()
    local json_data = rapidjson.load(OUTPUT_CAMERA_FILE_PATH)
    if json_data then
        -- 设置相机数据
        video_config = json_data
        native_print("成功加载合并数据文件")
        native_print("缩放因子:" .. scaleFactorFrom3DE)

        -- 加载视频数据（如果存在）
        if json_data.video and json_data.video.plane then
            local plane = json_data.video.plane

            -- 读取位置参数
            if plane.position then
                GROUND_POSITION_X = plane.position[1]
                GROUND_POSITION_Y = plane.position[2]
                GROUND_POSITION_Z = plane.position[3]
            else
                native_print("警告: 未找到地面位置数据")
            end

            -- 读取旋转参数（JSON中是角度单位）
            if plane.rotation then
                GROUND_ROTATION_X = plane.rotation[1]
                GROUND_ROTATION_Y = plane.rotation[2]
                GROUND_ROTATION_Z = plane.rotation[3]
            else
                native_print("警告: 未找到地面旋转数据")
            end

            -- 设置前景视频面片参数
            if plane.position then
                FG_VIDEO_QUAD_POSITION_X = plane.position[1]
                FG_VIDEO_QUAD_POSITION_Y = plane.position[2]
                FG_VIDEO_QUAD_POSITION_Z = plane.position[3]
            else
                native_print("警告: 未找到视频平面位置数据")
            end

            if plane.rotation then
                FG_VIDEO_QUAD_ROTATION_X = plane.rotation[1]
                FG_VIDEO_QUAD_ROTATION_Y = plane.rotation[2]
                FG_VIDEO_QUAD_ROTATION_Z = plane.rotation[3]
            else
                native_print("警告: 未找到视频平面旋转数据")
            end

            if plane.size then
                FG_VIDEO_QUAD_SIZE = (plane.size[1]) * 2 * GRID_SIZE
            else
                native_print("警告: 未找到视频平面大小数据")
            end

            native_print("成功加载视频数据配置")
        else
            native_print("未找到视频数据，使用默认值")
        end

    else
        native_print("警告: 无法加载合并数据文件")
    end
end

-- 根据相机数据创建第一人称视图矩阵
local function createFirstPersonViewMatrix(frameIndex)
    if not video_config or not video_config.camera or not video_config.camera.frames then
        return nil
    end

    local frames = video_config.camera.frames
    if frameIndex < 1 or frameIndex > #frames then
        return nil
    end

    local frameData = frames[frameIndex]
    if not frameData.location or not frameData.rotation_euler then
        return nil
    end

    -- 获取相机位置（应用缩放因子）
    local cameraPos = Vector3.new(frameData.location[1] * scaleFactorFrom3DE,
        frameData.location[2] * scaleFactorFrom3DE, frameData.location[3] * scaleFactorFrom3DE)

    -- 获取旋转角度（弧度制）
    local rotX = frameData.rotation_euler[1]
    local rotY = frameData.rotation_euler[2]
    local rotZ = frameData.rotation_euler[3]

    -- 使用BK框架标准方法创建旋转矩阵（ZYX顺序）
    local rotMatrixX = Matrix.new()
    local rotMatrixY = Matrix.new()
    local rotMatrixZ = Matrix.new()
    local cameraRotMatrix = Matrix.new()

    Matrix.createRotationX(rotX, rotMatrixX)
    Matrix.createRotationY(rotY, rotMatrixY)
    Matrix.createRotationZ(rotZ, rotMatrixZ)

    -- ZYX顺序相乘：cameraRotMatrix = rotMatrixZ * rotMatrixY * rotMatrixX
    cameraRotMatrix:set(rotMatrixZ)
    cameraRotMatrix:multiply(rotMatrixY)
    cameraRotMatrix:multiply(rotMatrixX)

    -- 获取相机的方向向量
    local forwardVector = Vector3.new()
    local upVector = Vector3.new()
    cameraRotMatrix:getForwardVector(forwardVector)
    cameraRotMatrix:getUpVector(upVector)

    -- 计算目标点（相机位置 + 前方向）
    local targetPos = Vector3.new(cameraPos:x() + forwardVector:x(), cameraPos:y() + forwardVector:y(),
        cameraPos:z() + forwardVector:z())

    -- 创建视图矩阵
    local viewMatrix = Matrix.new()
    Matrix.createLookAt(cameraPos, targetPos, upVector, viewMatrix)

    return viewMatrix
end

-- 计算屏幕在3D空间中的尺寸（需要在calculateScreenAndForegroundCorners之后调用）
local screen3D_width = 0.0
local screen3D_height = 0.0

-- 计算广告牌参数
local function calculateBillboardParams()
    -- 根据视频比例计算面片基础尺寸
    local baseQuadWidth, baseQuadHeight
    if FG_VIDEO_ASPECT_RATIO > 1.0 then
        -- 横向视频：宽度为基础大小，高度按比例缩放
        baseQuadWidth = FG_VIDEO_QUAD_SIZE
        baseQuadHeight = FG_VIDEO_QUAD_SIZE / FG_VIDEO_ASPECT_RATIO
    else
        -- 纵向视频：高度为基础大小，宽度按比例缩放
        baseQuadWidth = FG_VIDEO_QUAD_SIZE * FG_VIDEO_ASPECT_RATIO
        baseQuadHeight = FG_VIDEO_QUAD_SIZE
    end

    -- 应用屏幕对齐缩放
    local alignedQuadWidth = baseQuadWidth * FG_SCREEN_ALIGN_SCALE
    local alignedQuadHeight = baseQuadHeight * FG_SCREEN_ALIGN_SCALE

    -- 应用用户缩放
    local finalQuadWidth = alignedQuadWidth * FG_USER_ADJUST_SCALE
    local finalQuadHeight = alignedQuadHeight * FG_USER_ADJUST_SCALE

    -- 计算用户位移（以屏幕尺寸为单位）
    local userOffsetX = FG_USER_ADJUST_X * screen3D_width
    local userOffsetY = FG_USER_ADJUST_Y * screen3D_height

    -- 计算广告牌中心位置（重要：需要考虑原始面片的几何中心位置）
    -- 原始代码中，面片的几何中心Y坐标 = 底部Y + 高度/2
    local baseCenterX = FG_VIDEO_QUAD_POSITION_X + FG_SCREEN_ALIGN_X
    local baseCenterY = FG_VIDEO_QUAD_POSITION_Y + FG_SCREEN_ALIGN_Y + alignedQuadHeight / 2.0

    -- 应用用户偏移到几何中心
    local centerX = baseCenterX + userOffsetX
    local centerY = baseCenterY + userOffsetY
    local centerZ = FG_VIDEO_QUAD_POSITION_Z

    return centerX, centerY, centerZ, finalQuadWidth, finalQuadHeight
end
-- 从视图矩阵中提取相机位置（用于广告牌矩阵计算）
local function extractCameraPositionFromViewMatrix(viewMatrix)
    -- 创建视图矩阵的副本并求逆
    local invViewMatrix = Matrix.new()
    invViewMatrix:set(viewMatrix)
    local invertSuccess = invViewMatrix:invert()

    if not invertSuccess then
        native_print("警告: 无法计算视图矩阵的逆矩阵，使用默认相机位置")
        return Vector3.new(0.0, 0.0, 0.0)
    end

    -- 从逆视图矩阵的第4列提取相机位置
    local invViewMatrixData = invViewMatrix:mDataArray()
    -- 矩阵数据按列主序存储，第4列的位置分量在索引13, 14, 15（1-based）
    local cameraX = invViewMatrixData[13]
    local cameraY = invViewMatrixData[14]
    local cameraZ = invViewMatrixData[15]

    return Vector3.new(cameraX, cameraY, cameraZ)
end

-- 实现atan2函数（Lua标准库中没有）
local function atan2(y, x)
    if x > 0 then
        return math.atan(y / x)
    elseif x < 0 then
        if y >= 0 then
            return math.atan(y / x) + math.pi
        else
            return math.atan(y / x) - math.pi
        end
    else -- x == 0
        if y > 0 then
            return math.pi / 2
        elseif y < 0 then
            return -math.pi / 2
        else
            return 0 -- 未定义，但返回0
        end
    end
end

-- 计算圆柱形广告牌变换矩阵（只绕Y轴旋转，保持垂直）
local function calculateBillboardMatrix(billboardCenter, billboardSize, cameraPosition)
    -- 计算从广告牌中心到相机的方向向量（只考虑XZ平面）
    local centerX, centerY, centerZ = billboardCenter[1], billboardCenter[2], billboardCenter[3]
    local cameraX, cameraY, cameraZ = cameraPosition:x(), cameraPosition:y(), cameraPosition:z()

    -- 计算XZ平面上的方向向量（忽略Y分量）
    local dirX = cameraX - centerX
    local dirZ = cameraZ - centerZ
    local dirLength = math.sqrt(dirX * dirX + dirZ * dirZ)

    -- 避免除零错误
    if dirLength < 0.0001 then
        dirX, dirZ = 0.0, 1.0
        dirLength = 1.0
    else
        dirX = dirX / dirLength
        dirZ = dirZ / dirLength
    end

    -- 计算旋转角度（绕Y轴）
    local rotationY = atan2(dirX, dirZ)

    -- 创建旋转矩阵（只绕Y轴旋转）
    local rotationMatrix = Matrix.new()
    Matrix.createRotationY(rotationY, rotationMatrix)

    -- 创建缩放矩阵
    local scaleMatrix = Matrix.new()
    Matrix.createScale(billboardSize[1], billboardSize[2], 1.0, scaleMatrix)

    -- 创建平移矩阵
    local translationMatrix = Matrix.new()
    Matrix.createTranslation(centerX, centerY, centerZ, translationMatrix)

    -- 组合变换：平移 * 旋转 * 缩放（从右到左应用）
    local billboardMatrix = Matrix.new()
    billboardMatrix:set(translationMatrix)
    billboardMatrix:multiply(rotationMatrix)
    billboardMatrix:multiply(scaleMatrix)

    return billboardMatrix
end

-- ============================================================================
-- 预计算矩阵函数
-- ============================================================================
-- 预计算所有帧的矩阵数据
local function precomputeAllMatrices()
    native_print("开始预计算广告牌矩阵数据...")

    if not video_config or not video_config.camera or not video_config.camera.frames then
        native_print("警告: 无法获取相机数据，跳过矩阵预计算")
        return
    end

    local frames = video_config.camera.frames
    precomputedMatrices.totalFrames = #frames

    -- 预计算广告牌参数（所有帧共用）
    local centerX, centerY, centerZ, quadWidth, quadHeight = calculateBillboardParams()
    local billboardCenter = {centerX, centerY, centerZ}
    local billboardSize = {quadWidth, quadHeight}

    -- 为每一帧预计算广告牌矩阵
    for frameIndex = 1, precomputedMatrices.totalFrames do
        -- 计算视图矩阵
        local viewMatrix = Matrix.new()
        local firstPersonViewMatrix = createFirstPersonViewMatrix(frameIndex)
        if firstPersonViewMatrix then
            viewMatrix = firstPersonViewMatrix
        else
            viewMatrix:setIdentity()
        end

        -- 计算相机位置
        local cameraPosition = extractCameraPositionFromViewMatrix(viewMatrix)
        precomputedMatrices.cameraPositions[frameIndex] = cameraPosition

        -- 计算广告牌矩阵
        local billboardMatrix = calculateBillboardMatrix(billboardCenter, billboardSize, cameraPosition)
        precomputedMatrices.billboardMatrices[frameIndex] = billboardMatrix

        -- 存储广告牌参数（所有帧相同）
        precomputedMatrices.billboardParams[frameIndex] = {
            centerX = centerX,
            centerY = centerY,
            centerZ = centerZ,
            quadWidth = quadWidth,
            quadHeight = quadHeight
        }
    end

    native_print("广告牌矩阵预计算完成，总共 " .. precomputedMatrices.totalFrames .. " 帧")
end

-- 创建背景图绘制DrawCell（作为底图）
local function createBackgroundDrawCell()
    local drawCell = BKCommonDrawCell.new()

    -- 使用专用的背景着色器来绘制背景纹理（不翻转画面）
    drawCell:setProgramUsingShaderFile(gResourceRootPath .. "/resources/shaders/background",
        gResourceRootPath .. "/resources/shaders/background")

    -- 创建全屏四边形的顶点数据
    local bg_positionDataArray = {-1.0, -1.0, 0.0, 1.0, -1.0, 0.0, 1.0, 1.0, 0.0, -1.0, 1.0, 0.0}
    local bg_texCoordDataArray = {0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0}

    drawCell:addVertexAttribute("a_position", bg_positionDataArray, 4, BKDataType.BKFloat3, false)
    drawCell:addVertexAttribute("a_texCoord", bg_texCoordDataArray, 4, BKDataType.BKFloat2, false)

    -- 使用背景视频纹理作为输入
    drawCell:addInputTextureWithName("inputTexture", TEXTURE_NAMES.BG_TEXTURE, false, false)

    local bg_drawingIndicesDataArray = {0, 1, 2, 0, 2, 3}
    drawCell:setDrawingIndicesData(bg_drawingIndicesDataArray, 6)
    drawCell:setDrawingMode(BKDrawingMode.BKDrawingModeElements)

    -- 设置为清空背景，作为底图
    drawCell:setLoadAction(BKLoadAction.BKLoadActionClear)
    drawCell:setClearColor(0.0, 0.0, 0.0, 1.0) -- 黑色背景
    drawCell:setDepthTestEnable(false)

    -- 添加到渲染链
    gContextWrapper:addChild(drawCell)

    native_print("背景图绘制DrawCell已创建")
    return drawCell
end
-- 视频媒体信息回调（优化版本：使用预计算矩阵）
local function videoMediaCallBack(content)
    local cmdObj = rapidjson.decode(content)
    if cmdObj.code ~= nil then
        if cmdObj.code == 10 then
            local frameIndex = cmdObj.message + 1
            if isPauseFirstFrame then
                BackgroundVideoMeida:pause()
            end

            -- 更新当前相机帧索引
            currentCameraFrame = frameIndex

            -- 检查预计算矩阵是否可用
            if precomputedMatrices.totalFrames > 0 and frameIndex <= precomputedMatrices.totalFrames then
                -- ===== 优化：使用预计算的MoveGreenScreen矩阵参数 =====
                if camera_move_gs then
                    -- 获取预计算的广告牌矩阵
                    local billboardMatrix = precomputedMatrices.billboardMatrices[frameIndex]

                    -- 计算当前帧的视图矩阵和投影矩阵
                    local viewMatrix = Matrix.new()
                    local firstPersonViewMatrix = createFirstPersonViewMatrix(frameIndex)
                    if firstPersonViewMatrix then
                        viewMatrix = firstPersonViewMatrix
                    else
                        viewMatrix:setIdentity()
                    end

                    local projectionMatrix = Matrix.new()
                    Matrix.createPerspective(CAMERA_FOV, CAMERA_ASPECT_RATIO, NEAR_PLANE, FAR_PLANE, projectionMatrix)

                    -- 更新变换矩阵
                    camera_move_gs:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)

                    -- 更新ProjectionDrawCell（与前景面片同步）
                    if projectionDrawCell then
                        projectionDrawCell:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)
                    end
                end
            else
                -- 回退到原始计算方式（用于调试或预计算失败的情况）
                native_print("警告: 预计算矩阵不可用，使用实时计算 (帧: " .. frameIndex .. ")")

                -- Model矩阵（单位矩阵，物体不做变换）
                local modelMatrix = Matrix.new()
                modelMatrix:setIdentity()

                local viewMatrix = Matrix.new()

                -- 使用相机数据创建第一人称视图矩阵
                local firstPersonViewMatrix = createFirstPersonViewMatrix(currentCameraFrame)
                if firstPersonViewMatrix then
                    viewMatrix = firstPersonViewMatrix
                else
                    -- 如果无法获取相机数据，使用默认视图矩阵
                    native_print("警告: 无法获取相机数据，使用默认视图矩阵")
                    viewMatrix:setIdentity()
                end

                -- 创建投影矩阵
                local projectionMatrix = Matrix.new()
                Matrix.createPerspective(CAMERA_FOV, CAMERA_ASPECT_RATIO, NEAR_PLANE, FAR_PLANE, projectionMatrix)

                -- 更新MoveGreenScreen的矩阵参数
                if camera_move_gs then
                    -- 获取广告牌参数
                    local centerX, centerY, centerZ, quadWidth, quadHeight = calculateBillboardParams()

                    -- 从视图矩阵中提取相机位置
                    local cameraPosition = extractCameraPositionFromViewMatrix(viewMatrix)

                    -- 计算广告牌变换矩阵
                    local billboardCenter = {centerX, centerY, centerZ}
                    local billboardSize = {quadWidth, quadHeight}
                    local billboardMatrix = calculateBillboardMatrix(billboardCenter, billboardSize, cameraPosition)

                    -- 更新变换矩阵
                    camera_move_gs:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)

                    -- 更新ProjectionDrawCell（与前景面片同步）
                    if projectionDrawCell then
                        projectionDrawCell:updateMatrices(viewMatrix, projectionMatrix, billboardMatrix)
                    end
                end
            end
        end
    end
end
-- 创建视频播放器
local function createBackgroundVideoPlayer()
    -- 创建背景纹理节点，而不是直接添加到gContextWrapper
    local bg_tex = RenderTargetNode.createWithSize(TEXTURE_NAMES.BG_TEXTURE, CANVAS_Width, CANVAS_Height)
    gContextWrapper:addChild(bg_tex)

    BackgroundVideoMeida = BKMediaPlayer.new()
    bg_tex:addChild(BackgroundVideoMeida) -- 添加到纹理节点
    BackgroundVideoMeida:setMediaListener(videoMediaCallBack)
    BackgroundVideoMeida:init(BG_VIDEO_FILE_PATH)
    BackgroundVideoMeida:setVolume(0)
    BackgroundVideoMeida:play(-1)

    local videoWidth = BackgroundVideoMeida:getVideoWidth()
    local videoHeight = BackgroundVideoMeida:getVideoHeight()
    BackgroundVideoMeida:setAnchorPoint(Vector2.new(0.5, 0.5))
    BackgroundVideoMeida:setPosition(Vector2.new(videoWidth / 2, videoHeight / 2))
    BackgroundVideoMeida:setScaleY(-1)

    -- 调整背景纹理尺寸
    bg_tex:setSize(videoWidth, videoHeight, true)

    return BackgroundVideoMeida
end

-- 创建前景视频播放器（仅加载视频，不创建MoveGreenScreen节点）
local function createForegroundVideoPlayer()
    -- 创建原始视频纹理
    local fg_raw_tex = RenderTargetNode.createWithSize(TEXTURE_NAMES.FG_VIDEO_RAW, CANVAS_Width, CANVAS_Height)
    gContextWrapper:addChild(fg_raw_tex)

    local videoMeida = BKMediaPlayer.new()
    fg_raw_tex:addChild(videoMeida)
    videoMeida:init(gResourceRootPath .. FG_VIDEO_FILE_PATH)
    videoMeida:setVolume(0)
    videoMeida:play(-1)

    -- 提取视频的实际尺寸并更新全局变量
    local videoWidth = videoMeida:getVideoWidth()
    local videoHeight = videoMeida:getVideoHeight()

    -- 更新全局视频信息
    FG_VIDEO_WIDTH = videoWidth
    FG_VIDEO_HEIGHT = videoHeight
    FG_VIDEO_ASPECT_RATIO = videoWidth / videoHeight

    native_print("原始前景视频已加载: " .. videoWidth .. "x" .. videoHeight)

    fg_raw_tex:setSize(videoWidth, videoHeight, true)
    videoMeida:setAnchorPoint(Vector2.new(0.5, 0.5))
    videoMeida:setPosition(Vector2.new(videoWidth / 2, videoHeight / 2))
    videoMeida:setScaleY(-1)

    native_print("前景视频播放器创建完成，等待计算对齐参数后创建MoveGreenScreen节点")

end

-- 创建绿幕渲染流程（在对齐参数计算完成后调用）
local function createGreenScreen(centerX, centerY, centerZ, quadWidth, quadHeight)
    -- 创建绿幕抠像节点
    effect_GreenScreenMatting = GreenScreenMatting.new({
        path = gspath .. '/effect',
        inputTexName = TEXTURE_NAMES.FG_VIDEO_RAW,
        inputTexSize = {FG_VIDEO_WIDTH, FG_VIDEO_HEIGHT},
        outputTexName = TEXTURE_NAMES.FG_TEX,
        outputDetailMaskName = TEXTURE_NAMES.MATTING_DETAIL_MASK,
        outputAlphaMaskName = TEXTURE_NAMES.MATTING_ALPHA_MASK
    })
    gContextWrapper:addChild(effect_GreenScreenMatting)

    native_print("开始设置绿幕抠像参数...")
    -- 亮度调整
    effect_GreenScreenMatting:setGamma(greenscreen_params.gamma)
    effect_GreenScreenMatting:setContrast(greenscreen_params.contrast)
    effect_GreenScreenMatting:setBrightness(greenscreen_params.brightness)

    -- 基础抠像参数
    effect_GreenScreenMatting:setColor(greenscreen_params.key_color)
    effect_GreenScreenMatting:setSimilarity(greenscreen_params.similarity)
    effect_GreenScreenMatting:setEdgeIndent(greenscreen_params.edge_indent)
    effect_GreenScreenMatting:setRadius(greenscreen_params.radius)
    effect_GreenScreenMatting:setFxaaSmooth(greenscreen_params.fxaa_smooth)

    -- 溢色抑制参数
    effect_GreenScreenMatting:setSpillSupFlag(greenscreen_params.spill_sup_flag)
    effect_GreenScreenMatting:setSpillSupScaleR(greenscreen_params.spill_sup_scale_r)
    effect_GreenScreenMatting:setSpillSupScaleG(greenscreen_params.spill_sup_scale_g)
    effect_GreenScreenMatting:setSpillSupScaleB(greenscreen_params.spill_sup_scale_b)
    effect_GreenScreenMatting:setSpillSupRedW(greenscreen_params.spill_sup_red_w)
    effect_GreenScreenMatting:setSpillSupMix(greenscreen_params.spill_sup_mix)

    -- 透明度调整
    effect_GreenScreenMatting:setKeyGain(greenscreen_params.key_gain)
    effect_GreenScreenMatting:setKeyLift(greenscreen_params.key_lift)

    native_print("绿幕抠像参数设置完成")
    native_print("绿幕抠像节点已创建")

    -- 创建MoveGreenScreen节点
    camera_move_gs = MoveGreenScreen.new({
        inputTextureName = TEXTURE_NAMES.FG_TEX,
        outputTextureName = TEXTURE_NAMES.MOVE_GREENSCREEN_TEXTURE,
        canvasSize = {CANVAS_Width, CANVAS_Height}, -- 使用初始化时的画布尺寸
        cameraParams = {
            fov = CAMERA_FOV,
            aspect = CAMERA_ASPECT_RATIO,
            near = NEAR_PLANE,
            far = FAR_PLANE
        },
        billboardParams = {
            centerX = centerX,
            centerY = centerY,
            centerZ = centerZ,
            quadWidth = quadWidth,
            quadHeight = quadHeight
        }
    })
    gContextWrapper:addChild(camera_move_gs:getOutputNode())
    native_print("MoveGreenScreen节点已创建")

    -- ===== 新增：添加ProjectionDrawCell节点 =====
    projectionDrawCell = ProjectionDrawCell.new({
        inputTextureName = TEXTURE_NAMES.BG_TEXTURE,
        maskTextureName = TEXTURE_NAMES.MATTING_ALPHA_MASK,
        outputTextureName = TEXTURE_NAMES.PROJECTION_BG_TEXTURE,
        canvasSize = {CANVAS_Width, CANVAS_Height},
        greenScreenTexSize = {FG_VIDEO_WIDTH, FG_VIDEO_HEIGHT},
        projectionParams = {
            enabled = PROJECTION_ENABLED,
            directionPitch = PROJECTION_DIRECTION_PITCH,
            directionYaw = PROJECTION_DIRECTION_YAW,
            intensity = PROJECTION_INTENSITY,
            maxDistance = PROJECTION_MAX_DISTANCE,
            color = PROJECTION_COLOR,
            shadowGroundHeight = PROJECTION_SHADOW_GROUND_HEIGHT,
            depthCompare = PROJECTION_DEPTH_COMPARE,
            depthRedundancy = PROJECTION_DEPTH_REDUNDANCY,
            rescale = PROJECTION_RESCALE
        },
        groundParams = {
            positionX = GROUND_POSITION_X,
            positionY = GROUND_POSITION_Y,
            positionZ = GROUND_POSITION_Z,
            rotationX = GROUND_ROTATION_X,
            rotationY = GROUND_ROTATION_Y,
            rotationZ = GROUND_ROTATION_Z
        }
    })
    gContextWrapper:addChild(projectionDrawCell)
    native_print("ProjectionDrawCell节点已创建")

    -- ===== 新增：添加BackgroundBlend节点 =====
    effect_BackgroundBlend = BackgroundBlend.new({
        path = gspath .. '/effect',
        inputMattingName = TEXTURE_NAMES.MOVE_GREENSCREEN_TEXTURE, -- 使用变换后的前景
        inputBackgroundName = TEXTURE_NAMES.BG_TEXTURE,
        outputBackgroundName = TEXTURE_NAMES.BG_BLEND_OUTPUT,
        inputTexSize = {CANVAS_Width, CANVAS_Height}, -- 画布尺寸
        canvasSize = {CANVAS_Width, CANVAS_Height}
    })
    gContextWrapper:addChild(effect_BackgroundBlend)
    effect_BackgroundBlend:setIntensity(background_blend_intensity)
    native_print("BackgroundBlend节点已创建")

    -- ===== 新增：添加DetailBlend节点 =====
    effect_DetailBlend = DetailBlend.new({
        path = gspath .. '/effect',
        inputMattingName = TEXTURE_NAMES.BG_BLEND_OUTPUT, -- 始终使用背景融合输出
        inputDetailMaskName = TEXTURE_NAMES.MATTING_DETAIL_MASK,
        inputBackgroundName = TEXTURE_NAMES.PROJECTION_BG_TEXTURE, -- 始终使用投影背景
        outputBlendName = TEXTURE_NAMES.DETAIL_BLEND_OUTPUT,
        inputTexSize = {FG_VIDEO_WIDTH, FG_VIDEO_HEIGHT}, -- 前景尺寸
        canvasSize = {CANVAS_Width, CANVAS_Height} -- 画布尺寸
    })
    gContextWrapper:addChild(effect_DetailBlend)
    effect_DetailBlend:setDetail(detail_blend_strength)
    native_print("DetailBlend节点已创建")

    -- 设置Y轴翻转着色器（针对OpenGL）
    local fydrawCell = BKCommonDrawCell.new()
    fydrawCell:setProgramUsingShaderFile(gResourceRootPath .. "/resources/shaders/flip_y",
        gResourceRootPath .. "/resources/shaders/flip_y")

    -- 创建全屏四边形的顶点数据
    local flipY_positionDataArray = {-1.0, -1.0, 0.0, 1.0, -1.0, 0.0, 1.0, 1.0, 0.0, -1.0, 1.0, 0.0}
    local flipY_texCoordDataArray = {0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0}

    fydrawCell:addVertexAttribute("a_position", flipY_positionDataArray, 4, BKDataType.BKFloat3, false)
    fydrawCell:addVertexAttribute("a_texCoord", flipY_texCoordDataArray, 4, BKDataType.BKFloat2, false)
    fydrawCell:addInputTextureWithName("inputTexture", TEXTURE_NAMES.DETAIL_BLEND_OUTPUT, false, false)

    local flipY_drawingIndicesDataArray = {0, 1, 2, 0, 2, 3}
    fydrawCell:setDrawingIndicesData(flipY_drawingIndicesDataArray, 6)
    fydrawCell:setDrawingMode(BKDrawingMode.BKDrawingModeElements)

    fydrawCell:setLoadAction(BKLoadAction.BKLoadActionCopyInput)
    fydrawCell:enableDefaultBlend()
    fydrawCell:setDepthTestEnable(false)
    gContextWrapper:addChild(fydrawCell)
end

-- 计算屏幕四个角点和前景面片四个角点的3D位置，并返回屏幕对齐参数
local function calculateScreenAndForegroundCorners()
    native_print("=== 计算屏幕角点和前景面片角点的3D位置 ===")

    -- 获取当前的投影矩阵和视图矩阵
    local projectionMatrix = Matrix.new()
    Matrix.createPerspective(CAMERA_FOV, CAMERA_ASPECT_RATIO, NEAR_PLANE, FAR_PLANE, projectionMatrix)

    local viewMatrix = Matrix.new()
    local firstPersonViewMatrix = createFirstPersonViewMatrix(currentCameraFrame)
    if firstPersonViewMatrix then
        viewMatrix = firstPersonViewMatrix
    else
        viewMatrix:setIdentity()
    end

    -- 计算MVP矩阵的逆矩阵
    local mvpMatrix = Matrix.new()
    mvpMatrix:set(projectionMatrix)
    mvpMatrix:multiply(viewMatrix)

    local invMvpMatrix = Matrix.new()
    invMvpMatrix:set(mvpMatrix)
    local invertSuccess = invMvpMatrix:invert()

    if not invertSuccess then
        native_print("错误: 无法计算MVP矩阵的逆矩阵")
        return 0.0, 0.0, 1.0 -- 返回默认值
    end

    -- 定义屏幕四个角点的NDC坐标 (x, y, z, w)
    -- 我们需要计算两个深度：近平面和远平面，然后计算射线与前景面片Z平面的交点
    local screenCorners = {{-1.0, -1.0, -1.0, 1.0}, -- 左下角 (近平面)
    {1.0, -1.0, -1.0, 1.0}, -- 右下角 (近平面)
    {1.0, 1.0, -1.0, 1.0}, -- 右上角 (近平面)
    {-1.0, 1.0, -1.0, 1.0} -- 左上角 (近平面)
    }

    local screenCornersFar = {{-1.0, -1.0, 1.0, 1.0}, -- 左下角 (远平面)
    {1.0, -1.0, 1.0, 1.0}, -- 右下角 (远平面)
    {1.0, 1.0, 1.0, 1.0}, -- 右上角 (远平面)
    {-1.0, 1.0, 1.0, 1.0} -- 左上角 (远平面)
    }

    native_print("屏幕四个角点对应的3D世界坐标 (固定Z=" .. FG_VIDEO_QUAD_POSITION_Z .. "):")

    for i = 1, 4 do
        -- 计算近平面点
        local nearCorner = screenCorners[i]
        local nearNdcPos = Vector4.new(nearCorner[1], nearCorner[2], nearCorner[3], nearCorner[4])
        local nearWorldPos = Vector4.new()
        invMvpMatrix:transformVector(nearNdcPos, nearWorldPos)

        -- 透视除法
        if nearWorldPos:w() ~= 0 then
            nearWorldPos:set(nearWorldPos:x() / nearWorldPos:w(), nearWorldPos:y() / nearWorldPos:w(),
                nearWorldPos:z() / nearWorldPos:w(), 1.0)
        end

        -- 计算远平面点
        local farCorner = screenCornersFar[i]
        local farNdcPos = Vector4.new(farCorner[1], farCorner[2], farCorner[3], farCorner[4])
        local farWorldPos = Vector4.new()
        invMvpMatrix:transformVector(farNdcPos, farWorldPos)

        -- 透视除法
        if farWorldPos:w() ~= 0 then
            farWorldPos:set(farWorldPos:x() / farWorldPos:w(), farWorldPos:y() / farWorldPos:w(),
                farWorldPos:z() / farWorldPos:w(), 1.0)
        end

        -- 计算射线方向
        local rayStart = Vector3.new(nearWorldPos:x(), nearWorldPos:y(), nearWorldPos:z())
        local rayEnd = Vector3.new(farWorldPos:x(), farWorldPos:y(), farWorldPos:z())
        local rayDirection =
            Vector3.new(rayEnd:x() - rayStart:x(), rayEnd:y() - rayStart:y(), rayEnd:z() - rayStart:z())

        -- 计算射线与Z=FG_VIDEO_QUAD_POSITION_Z平面的交点
        local finalX, finalY, finalZ
        if math.abs(rayDirection:z()) > 0.0001 then
            local t = (FG_VIDEO_QUAD_POSITION_Z - rayStart:z()) / rayDirection:z()
            finalX = rayStart:x() + rayDirection:x() * t
            finalY = rayStart:y() + rayDirection:y() * t
            finalZ = FG_VIDEO_QUAD_POSITION_Z
        else
            -- 射线平行于Z平面，使用近平面点
            finalX = rayStart:x()
            finalY = rayStart:y()
            finalZ = FG_VIDEO_QUAD_POSITION_Z
        end

        local cornerNames = {"左下角", "右下角", "右上角", "左上角"}
        native_print("  " .. cornerNames[i] .. ": (" .. finalX .. ", " .. finalY .. ", " .. finalZ .. ")")
    end

    -- 计算前景面片四个角点的3D位置
    native_print("前景面片四个角点的3D世界坐标:")

    -- 根据视频比例计算面片尺寸
    local quadWidth, quadHeight
    if FG_VIDEO_ASPECT_RATIO > 1.0 then
        quadWidth = FG_VIDEO_QUAD_SIZE
        quadHeight = FG_VIDEO_QUAD_SIZE / FG_VIDEO_ASPECT_RATIO
    else
        quadWidth = FG_VIDEO_QUAD_SIZE * FG_VIDEO_ASPECT_RATIO
        quadHeight = FG_VIDEO_QUAD_SIZE
    end

    local halfWidth = quadWidth / 2.0

    -- 定义四个顶点的本地坐标（以底部中心为原点，面向+Z方向）
    local localPositions = {{-halfWidth, 0.0, 0.0}, -- 左下
    {halfWidth, 0.0, 0.0}, -- 右下
    {halfWidth, quadHeight, 0.0}, -- 右上
    {-halfWidth, quadHeight, 0.0} -- 左上
    }

    -- 创建旋转矩阵（ZYX顺序）
    local rotMatrixX = Matrix.new()
    local rotMatrixY = Matrix.new()
    local rotMatrixZ = Matrix.new()
    local finalRotMatrix = Matrix.new()

    -- 将度数转换为弧度
    local rotXRad = FG_VIDEO_QUAD_ROTATION_X * math.pi / 180.0
    local rotYRad = FG_VIDEO_QUAD_ROTATION_Y * math.pi / 180.0
    local rotZRad = FG_VIDEO_QUAD_ROTATION_Z * math.pi / 180.0

    Matrix.createRotationX(rotXRad, rotMatrixX)
    Matrix.createRotationY(rotYRad, rotMatrixY)
    Matrix.createRotationZ(rotZRad, rotMatrixZ)

    -- ZYX顺序相乘：finalRotMatrix = rotMatrixZ * rotMatrixY * rotMatrixX
    finalRotMatrix:set(rotMatrixZ)
    finalRotMatrix:multiply(rotMatrixY)
    finalRotMatrix:multiply(rotMatrixX)

    -- 应用旋转和位移变换到每个顶点
    for i = 1, 4 do
        local localPos = localPositions[i]
        local localVec = Vector3.new(localPos[1], localPos[2], localPos[3])

        -- 应用旋转变换
        local rotatedVec = Vector3.new()
        finalRotMatrix:transformVector(localVec, rotatedVec)

        -- 应用位移变换
        local finalX = rotatedVec:x() + FG_VIDEO_QUAD_POSITION_X
        local finalY = rotatedVec:y() + FG_VIDEO_QUAD_POSITION_Y
        local finalZ = rotatedVec:z() + FG_VIDEO_QUAD_POSITION_Z

        local cornerNames = {"左下角", "右下角", "右上角", "左上角"}
        native_print("  " .. cornerNames[i] .. ": (" .. finalX .. ", " .. finalY .. ", " .. finalZ .. ")")
    end

    -- 计算屏幕对齐参数
    native_print("=== 计算屏幕对齐参数 ===")
    local screenWorldCorners = {}
    for i = 1, 4 do
        -- 计算近平面点
        local nearCorner = screenCorners[i]
        local nearNdcPos = Vector4.new(nearCorner[1], nearCorner[2], nearCorner[3], nearCorner[4])
        local nearWorldPos = Vector4.new()
        invMvpMatrix:transformVector(nearNdcPos, nearWorldPos)

        if nearWorldPos:w() ~= 0 then
            nearWorldPos:set(nearWorldPos:x() / nearWorldPos:w(), nearWorldPos:y() / nearWorldPos:w(),
                nearWorldPos:z() / nearWorldPos:w(), 1.0)
        end

        -- 计算远平面点
        local farCorner = screenCornersFar[i]
        local farNdcPos = Vector4.new(farCorner[1], farCorner[2], farCorner[3], farCorner[4])
        local farWorldPos = Vector4.new()
        invMvpMatrix:transformVector(farNdcPos, farWorldPos)

        if farWorldPos:w() ~= 0 then
            farWorldPos:set(farWorldPos:x() / farWorldPos:w(), farWorldPos:y() / farWorldPos:w(),
                farWorldPos:z() / farWorldPos:w(), 1.0)
        end

        -- 计算射线与前景面片Z平面的交点
        local rayStart = Vector3.new(nearWorldPos:x(), nearWorldPos:y(), nearWorldPos:z())
        local rayEnd = Vector3.new(farWorldPos:x(), farWorldPos:y(), farWorldPos:z())
        local rayDirection =
            Vector3.new(rayEnd:x() - rayStart:x(), rayEnd:y() - rayStart:y(), rayEnd:z() - rayStart:z())

        local finalX, finalY
        if math.abs(rayDirection:z()) > 0.0001 then
            local t = (FG_VIDEO_QUAD_POSITION_Z - rayStart:z()) / rayDirection:z()
            finalX = rayStart:x() + rayDirection:x() * t
            finalY = rayStart:y() + rayDirection:y() * t
        else
            finalX = rayStart:x()
            finalY = rayStart:y()
        end

        screenWorldCorners[i] = {finalX, finalY}
    end

    -- 计算屏幕在3D空间中的边界
    local minX = math.min(screenWorldCorners[1][1], screenWorldCorners[2][1], screenWorldCorners[3][1],
        screenWorldCorners[4][1])
    local maxX = math.max(screenWorldCorners[1][1], screenWorldCorners[2][1], screenWorldCorners[3][1],
        screenWorldCorners[4][1])
    local minY = math.min(screenWorldCorners[1][2], screenWorldCorners[2][2], screenWorldCorners[3][2],
        screenWorldCorners[4][2])
    local maxY = math.max(screenWorldCorners[1][2], screenWorldCorners[2][2], screenWorldCorners[3][2],
        screenWorldCorners[4][2])

    local screenWidth3D = maxX - minX
    local screenHeight3D = maxY - minY
    local screenCenterX = (minX + maxX) / 2.0
    local screenCenterY = (minY + maxY) / 2.0

    -- 设置全局屏幕3D尺寸变量
    screen3D_width = screenWidth3D
    screen3D_height = screenHeight3D

    -- 计算缩放系数，使前景面片适配屏幕（保持比例，不裁剪）
    local scaleX = screenWidth3D / quadWidth
    local scaleY = screenHeight3D / quadHeight
    local alignScale = math.min(scaleX, scaleY) -- 使用较小的缩放系数确保前景完全显示在屏幕内

    -- 计算缩放后的面片尺寸
    local scaledQuadWidth = quadWidth * alignScale
    local scaledQuadHeight = quadHeight * alignScale

    -- 计算前景面片缩放后的几何中心位置（考虑到原点在底部中心）
    local quadGeometricCenterX = FG_VIDEO_QUAD_POSITION_X
    local quadGeometricCenterY = FG_VIDEO_QUAD_POSITION_Y + scaledQuadHeight / 2.0

    -- 计算对齐参数
    -- X轴：屏幕中心对齐到面片几何中心
    local alignX = screenCenterX - quadGeometricCenterX
    -- Y轴：屏幕中心对齐到面片几何中心
    local alignY = screenCenterY - quadGeometricCenterY

    native_print("屏幕3D边界: X[" .. minX .. ", " .. maxX .. "], Y[" .. minY .. ", " .. maxY .. "]")
    native_print("屏幕3D尺寸: " .. screenWidth3D .. " x " .. screenHeight3D)
    native_print("屏幕3D中心: (" .. screenCenterX .. ", " .. screenCenterY .. ")")
    native_print("前景面片原始尺寸: " .. quadWidth .. " x " .. quadHeight)
    native_print("前景面片缩放后尺寸: " .. scaledQuadWidth .. " x " .. scaledQuadHeight)
    native_print("前景面片原点位置: (" .. FG_VIDEO_QUAD_POSITION_X .. ", " .. FG_VIDEO_QUAD_POSITION_Y .. ")")
    native_print("前景面片缩放后几何中心: (" .. quadGeometricCenterX .. ", " .. quadGeometricCenterY .. ")")
    native_print("计算得到的对齐参数:")
    native_print("  X偏移: " .. alignX .. " (屏幕中心 - 面片几何中心X)")
    native_print("  Y偏移: " .. alignY .. " (屏幕中心 - 面片几何中心Y)")
    native_print("  缩放系数: " .. alignScale .. " (min(" .. scaleX .. ", " .. scaleY ..
                     ") - 适配屏幕不裁剪)")

    -- 验证：计算应用对齐参数后前景面片的实际位置
    native_print("=== 验证对齐参数 ===")

    -- 计算应用对齐参数后的前景面片四个角点位置
    local alignedQuadWidth = quadWidth * alignScale
    local alignedQuadHeight = quadHeight * alignScale
    local alignedHalfWidth = alignedQuadWidth / 2.0

    -- 应用对齐偏移后的面片原点位置
    local alignedQuadOriginX = FG_VIDEO_QUAD_POSITION_X + alignX
    local alignedQuadOriginY = FG_VIDEO_QUAD_POSITION_Y + alignY

    -- 计算对齐后面片的四个角点（考虑旋转）
    local alignedLocalPositions = {{-alignedHalfWidth, 0.0, 0.0}, -- 左下
    {alignedHalfWidth, 0.0, 0.0}, -- 右下
    {alignedHalfWidth, alignedQuadHeight, 0.0}, -- 右上
    {-alignedHalfWidth, alignedQuadHeight, 0.0} -- 左上
    }

    -- 计算对齐后前景面片的实际角点位置
    local alignedQuadCorners = {}
    for i = 1, 4 do
        local localPos = alignedLocalPositions[i]
        local localVec = Vector3.new(localPos[1], localPos[2], localPos[3])

        -- 应用旋转变换
        local rotatedVec = Vector3.new()
        finalRotMatrix:transformVector(localVec, rotatedVec)

        -- 应用位移变换（使用对齐后的原点位置）
        local finalX = rotatedVec:x() + alignedQuadOriginX
        local finalY = rotatedVec:y() + alignedQuadOriginY
        local finalZ = rotatedVec:z() + FG_VIDEO_QUAD_POSITION_Z

        alignedQuadCorners[i] = {finalX, finalY, finalZ}
    end

    -- 输出对齐后前景面片的角点位置
    native_print("对齐后前景面片四个角点的3D世界坐标:")
    local cornerNames = {"左下角", "右下角", "右上角", "左上角"}
    for i = 1, 4 do
        local corner = alignedQuadCorners[i]
        native_print("  " .. cornerNames[i] .. ": (" .. corner[1] .. ", " .. corner[2] .. ", " .. corner[3] .. ")")
    end

    -- 计算前景面片在屏幕投影平面上的边界
    local quadMinX = math.min(alignedQuadCorners[1][1], alignedQuadCorners[2][1], alignedQuadCorners[3][1],
        alignedQuadCorners[4][1])
    local quadMaxX = math.max(alignedQuadCorners[1][1], alignedQuadCorners[2][1], alignedQuadCorners[3][1],
        alignedQuadCorners[4][1])
    local quadMinY = math.min(alignedQuadCorners[1][2], alignedQuadCorners[2][2], alignedQuadCorners[3][2],
        alignedQuadCorners[4][2])
    local quadMaxY = math.max(alignedQuadCorners[1][2], alignedQuadCorners[2][2], alignedQuadCorners[3][2],
        alignedQuadCorners[4][2])

    local quadWidth3D = quadMaxX - quadMinX
    local quadHeight3D = quadMaxY - quadMinY
    local quadCenterX = (quadMinX + quadMaxX) / 2.0
    local quadCenterY = (quadMinY + quadMaxY) / 2.0

    -- 比较屏幕和前景面片的位置
    native_print("=== 详细尺寸和位置信息 ===")

    -- 输出画布和相机信息
    native_print("画布尺寸: " .. CANVAS_Width .. "x" .. CANVAS_Height)
    native_print("相机宽高比: " .. CAMERA_ASPECT_RATIO)
    native_print("相机视野角度: " .. CAMERA_FOV .. "°")

    -- 输出前景视频信息
    native_print("前景视频宽高比: " .. FG_VIDEO_ASPECT_RATIO)
    native_print("前景面片基础大小: " .. FG_VIDEO_QUAD_SIZE)
    native_print("前景面片原始尺寸: " .. quadWidth .. "x" .. quadHeight)
    native_print("前景面片位置: (" .. FG_VIDEO_QUAD_POSITION_X .. ", " .. FG_VIDEO_QUAD_POSITION_Y .. ", " ..
                     FG_VIDEO_QUAD_POSITION_Z .. ")")
    native_print("前景面片旋转: (" .. FG_VIDEO_QUAD_ROTATION_X .. "°, " .. FG_VIDEO_QUAD_ROTATION_Y .. "°, " ..
                     FG_VIDEO_QUAD_ROTATION_Z .. "°)")

    -- 输出屏幕3D信息
    native_print("屏幕3D边界: X[" .. minX .. ", " .. maxX .. "], Y[" .. minY .. ", " .. maxY .. "]")
    native_print("屏幕3D尺寸: " .. screenWidth3D .. "x" .. screenHeight3D)
    native_print("屏幕3D中心: (" .. screenCenterX .. ", " .. screenCenterY .. ")")
    native_print("屏幕3D宽高比: " .. (screenWidth3D / screenHeight3D))

    -- 输出缩放计算详情（重用之前计算的scaleX和scaleY）
    native_print("=== 缩放计算详情 ===")
    native_print("X方向缩放需求: " .. (screenWidth3D / quadWidth) .. " (屏幕宽度/前景宽度)")
    native_print("Y方向缩放需求: " .. (screenHeight3D / quadHeight) .. " (屏幕高度/前景高度)")
    native_print("选择的缩放系数: " .. alignScale .. " = min(scaleX, scaleY)")

    local currentScaleX = screenWidth3D / quadWidth
    local currentScaleY = screenHeight3D / quadHeight
    if currentScaleX < currentScaleY then
        native_print("缩放限制因素: 宽度 (前景会在上下留空)")
    elseif currentScaleY < currentScaleX then
        native_print("缩放限制因素: 高度 (前景会在左右留空)")
    else
        native_print("缩放限制因素: 宽高比完全匹配")
    end

    -- 输出对齐后前景面片信息
    native_print("=== 对齐后前景面片信息 ===")
    native_print("对齐后前景面片尺寸: " .. alignedQuadWidth .. "x" .. alignedQuadHeight)
    native_print("对齐后前景面片3D边界: X[" .. quadMinX .. ", " .. quadMaxX .. "], Y[" .. quadMinY .. ", " ..
                     quadMaxY .. "]")
    native_print("对齐后前景面片3D中心: (" .. quadCenterX .. ", " .. quadCenterY .. ")")
    native_print("对齐后前景面片宽高比: " .. (quadWidth3D / quadHeight3D))

    -- 计算空白区域
    local leftSpace = quadMinX - minX
    local rightSpace = maxX - quadMaxX
    local topSpace = maxY - quadMaxY
    local bottomSpace = quadMinY - minY

    native_print("=== 空白区域分析 ===")
    native_print("左侧空白: " .. leftSpace .. " (负值表示前景超出屏幕)")
    native_print("右侧空白: " .. rightSpace .. " (负值表示前景超出屏幕)")
    native_print("顶部空白: " .. topSpace .. " (负值表示前景超出屏幕)")
    native_print("底部空白: " .. bottomSpace .. " (负值表示前景超出屏幕)")

    -- 计算中心点偏差
    local centerOffsetX = math.abs(screenCenterX - quadCenterX)
    local centerOffsetY = math.abs(screenCenterY - quadCenterY)
    native_print("=== 对齐精度分析 ===")
    native_print("中心点偏差: X=" .. centerOffsetX .. ", Y=" .. centerOffsetY)

    -- 计算尺寸比较
    local sizeRatioX = quadWidth3D / screenWidth3D
    local sizeRatioY = quadHeight3D / screenHeight3D
    native_print("尺寸占用比例: 前景宽度/屏幕宽度=" .. sizeRatioX .. ", 前景高度/屏幕高度=" ..
                     sizeRatioY)

    -- 判断对齐质量
    local centerTolerance = 0.01 -- 中心点偏差容忍度
    local isCenterAligned = (centerOffsetX < centerTolerance) and (centerOffsetY < centerTolerance)
    -- 前景应该不超出屏幕
    local isProperlyScaled = (sizeRatioX <= 1.0) and (sizeRatioY <= 1.0)

    if isCenterAligned and isProperlyScaled then
        native_print("✓ 验证通过：前景面片正确对齐到屏幕中心且未超出屏幕边界")
    else
        native_print("✗ 验证警告：")
        if not isCenterAligned then
            native_print("  - 中心点未对齐，偏差超过容忍度")
        end
        if not isProperlyScaled then
            native_print("  - 前景面片尺寸超出屏幕边界")
        end
    end

    native_print("=== 计算完成 ===")

    return alignX, alignY, alignScale
end

function initialize(width, height, viewportX, viewportY, viewportWidth, viewportHeight, initialParams)
    -- ========================================
    -- ============= 设置画布尺寸 ==============
    -- ========================================
    -- 存储画布尺寸
    CANVAS_Width = width
    CANVAS_Height = height

    -- 更新相机宽高比
    CAMERA_ASPECT_RATIO = CANVAS_Width / CANVAS_Height

    -- ========================================
    -- ============= 创建前景/背景输入纹理 ==============
    -- ========================================
    -- 创建背景视频播放器
    createBackgroundVideoPlayer()

    -- 创建背景图绘制DrawCell（作为底图）
    createBackgroundDrawCell()

    -- 先创建前景视频播放器以获取真实的视频尺寸
    createForegroundVideoPlayer()

    -- ========================================
    -- ============= 计算相机参数 ==============
    -- ========================================
    -- 加载合并的数据文件（包含相机、点和视频数据）
    loadCombinedData()

    -- 计算相机参数
    calculateCameraParams()

    -- 计算屏幕对齐参数（必须在获取前景视频尺寸之后）
    local alignX, alignY, alignScale = calculateScreenAndForegroundCorners()
    if alignX and alignY and alignScale then
        FG_SCREEN_ALIGN_X = alignX
        FG_SCREEN_ALIGN_Y = alignY
        FG_SCREEN_ALIGN_SCALE = alignScale
        native_print(
            "屏幕对齐参数已设置: X=" .. FG_SCREEN_ALIGN_X .. ", Y=" .. FG_SCREEN_ALIGN_Y .. ", Scale=" ..
                FG_SCREEN_ALIGN_SCALE)
    else
        native_print("使用默认屏幕对齐参数")
    end

    -- 计算广告牌参数（此时对齐参数已经计算完成）
    local centerX, centerY, centerZ, quadWidth, quadHeight = calculateBillboardParams()

    -- 预计算所有帧的矩阵数据 
    -- 必须在MoveGreenScreen节点创建之后进行，因为需要calculateBillboardParams函数
    precomputeAllMatrices()

    -- ========================================
    -- ==== 创建绿幕渲染流程的所有节点 ==========
    -- ========================================
    -- 前景视频播放器已创建，对齐参数计算已完成
    createGreenScreen(centerX, centerY, centerZ, quadWidth, quadHeight)

end

local updateyaw = 0
---脚本主循环 每渲染一帧均会被调用
---@param elapsedTime number {comment = '距离初始化后经过的时间 毫秒为单位'}
---@param timeSinceLastFrame number {comment = '距离上一帧经过的时间 毫秒为单位'}
---@param detectionInfo DetectionInfo {comment = 'AI识别数据'}
function update(elapsedTime, timeSinceLastFrame, detectionInfo)
    -- 传递检测信息给DetailBlend节点（用于人脸检测优化）
    if effect_DetailBlend ~= nil then
        effect_DetailBlend:setDetectionInfo(detectionInfo)
    end

    -- 传递检测信息给GreenScreenMatting节点（保持原有功能）
    if effect_GreenScreenMatting ~= nil then
        effect_GreenScreenMatting:setDetectionInfo(detectionInfo)
    end

    if projectionDrawCell ~= nil then
        updateyaw = updateyaw + 0.1
        projectionDrawCell:updateProjectionParams({
            directionYaw = updateyaw
        })
    end
end

---触摸开始事件
---@param x number {comment = '基于画布坐标系的横坐标'}
---@param y number {comment = '基于画布坐标系的纵坐标'}
---@param pointer number {comment = '手指序号（通过序号可以处理多点触控效果）'}
function onTouchBegin(x, y, pointer)
end

---触摸移动事件
---@param x number {comment = '基于画布坐标系的横坐标'}
---@param y number {comment = '基于画布坐标系的纵坐标'}
---@param pointer number {comment = '手指序号（通过序号可以处理多点触控效果）'}
function onTouchMove(x, y, pointer)
end

---触摸结束事件
---@param x number {comment = '基于画布坐标系的横坐标'}
---@param y number {comment = '基于画布坐标系的纵坐标'}
---@param pointer number {comment = '手指序号（通过序号可以处理多点触控效果）'}
function onTouchEnd(x, y, pointer)
end

---外部命令回调
---@param content string {comment = '基于业务代表不同的含义'}
function onCommand(content)
end

---当前脚本析构时回调（特效被移除，特效切换等）
function finalize()
end

