#version 310 es

layout(location=0) in vec3 a_position;
layout(location=1) in vec4 a_color;
layout(location=2) in float a_objectType; // 0.0 = cube, 1.0 = ground, 4.0 = foreground video quad
layout(location=3) in vec2 a_texCoord;

layout(binding=0) uniform VertUniforms {
    mat4 modelMatrix;
    mat4 viewMatrix;
    mat4 projectionMatrix;
};

layout(location=0) out vec4 color;
layout(location=1) out float objectType;
layout(location=2) out vec3 worldPos;
layout(location=3) out vec2 texCoord;

void main()
{
    // 在shader内部分步计算MVP变换
    vec4 worldPosition = modelMatrix * vec4(a_position, 1.0);
    vec4 viewPosition = viewMatrix * worldPosition;
    gl_Position = projectionMatrix * viewPosition;

    color = a_color;
    objectType = a_objectType;
    worldPos = a_position;
    texCoord = a_texCoord;
}
