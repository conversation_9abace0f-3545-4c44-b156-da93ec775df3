#version 310 es

layout(location = 0) in vec3 a_position; // 本地坐标系位置（-0.5到0.5）
layout(location = 1) in vec2 a_texCoord; // 前景面片的原始纹理坐标

layout(binding = 0) uniform VertUniforms {
  mat4 modelMatrix;
  mat4 viewMatrix;
  mat4 projectionMatrix;
  mat4 billboardMatrix; // 前景面片的广告牌变换矩阵（共享）

  // 地面plane参数
  vec3 groundPlanePoint;  // 地面plane上的一点
  vec3 groundPlaneNormal; // 地面plane的法向量

  // 投影参数
  vec2 projectionRotation; // 投影方向的两个旋转角度（弧度）：x=俯仰角(pitch),
                           // y=偏航角(yaw)
  float projectionIntensity; // 投影强度
  float projectionDistance;  // 投影距离限制
  float shadowGroundHeight;  // 阴影离地面高度（垂直偏移）
};

layout(location = 0) out vec4 texCoord; // 背景纹理坐标（屏幕空间）
layout(location = 1) out vec2 maskTexCoord; // 掩模纹理坐标（前景面片原始坐标）
layout(location = 2) out float intensity;
layout(location = 3) out float originalDepth;
layout(location = 4) out float projectedDepth;

// 计算射线与平面的交点
vec3 rayPlaneIntersection(vec3 rayOrigin, vec3 rayDirection, vec3 planePoint,
                          vec3 planeNormal) {
  float denom = dot(planeNormal, rayDirection);

  // 检查射线是否平行于平面
  if (abs(denom) < 0.0001) {
    return rayOrigin; // 返回原点作为fallback
  }

  float t = dot(planePoint - rayOrigin, planeNormal) / denom;

  return rayOrigin + t * rayDirection;
}

// 根据俯仰角和偏航角以及地面法向量计算射线方向
vec3 calculateRayDirection(vec2 rotation, vec3 groundNormal) {
  // 构建地面平面的局部坐标系
  // 地面法向量作为局部Y轴
  vec3 localY = normalize(groundNormal);

  // 选择一个与法向量不平行的向量来构建切线
  vec3 tempVec = abs(localY.y) < 0.9 ? vec3(0.0, 1.0, 0.0) : vec3(1.0, 0.0, 0.0);
  vec3 localX = normalize(cross(tempVec, localY));
  vec3 localZ = normalize(cross(localY, localX));

  // 在局部坐标系中计算方向（相对于地面法向量）
  float cosPitch = cos(rotation.x); // rotation.x = 俯仰角(pitch)
  float sinPitch = sin(rotation.x);
  float cosYaw = cos(rotation.y); // rotation.y = 偏航角(yaw)
  float sinYaw = sin(rotation.y);

  // 在局部坐标系中的方向（默认沿着负法向量方向投影到地面）
  vec3 localDirection = vec3(sinYaw * cosPitch, -sinPitch, cosYaw * cosPitch);

  // 转换回世界坐标系
  vec3 worldDirection = localDirection.x * localX + localDirection.y * localY + localDirection.z * localZ;

  return normalize(worldDirection);
}

void main() {
  // 1. 计算前景面片角点的世界坐标（与前景面片相同的计算）
  vec4 foregroundWorldPos = billboardMatrix * vec4(a_position, 1.0);

  // 2. 计算投影射线方向（基于地面法向量）
  vec3 rayDirection = calculateRayDirection(projectionRotation, groundPlaneNormal);

  // 3. 计算射线与地面plane的交点
  vec3 projectedPos = rayPlaneIntersection(foregroundWorldPos.xyz, rayDirection,
                                           groundPlanePoint, groundPlaneNormal);

  // 4. 应用阴影离地面高度偏移
  // 沿着地面法向量方向偏移指定的高度
  projectedPos += groundPlaneNormal * shadowGroundHeight;

  // 5. 应用视图和投影变换
  mat4 mvp = projectionMatrix * viewMatrix;
  gl_Position = mvp * vec4(projectedPos, 1.0);

  // 6. 计算投影强度（基于距离衰减）
  float dist = length(projectedPos - foregroundWorldPos.xyz);
  float distanceFactor = 1.0 - clamp(dist / projectionDistance, 0.0, 1.0);

  texCoord =
      gl_Position; // 背景纹理使用屏幕坐标（直接传，不然会因为插值导致精度损失，效果出现异常）
  maskTexCoord = a_texCoord; // 掩模纹理使用前景面片原始坐标
  // intensity = projectionIntensity * (1.0 + 0.0001 * distanceFactor);
    intensity = projectionIntensity * distanceFactor;

  // 计算深度
  vec4 fgPostion = mvp * foregroundWorldPos;
  originalDepth = fgPostion.z;
  projectedDepth = gl_Position.z;
}
