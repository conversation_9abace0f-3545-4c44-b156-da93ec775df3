#version 310 es

precision mediump float;

layout(location = 0) in vec2 textureCoordinate;

layout(binding = 0) uniform sampler2D inputImageTexture;

layout(binding = 0) uniform FragUniforms {
  float baseTexWidth;
  float baseTexHeight;
  vec2 direction;
};

layout(location = 0) out vec4 gl_FragColor;

const float PI = 3.14159265359;

void main() {
  vec2 uv = textureCoordinate;
  vec2 unit_uv = 2.0 / vec2(baseTexWidth, baseTexHeight);

  int radius = 4;
  float half_gaussian_weight[5];

  half_gaussian_weight[0] = 0.2270270270;
  half_gaussian_weight[1] = 0.1945945946;
  half_gaussian_weight[2] = 0.1216216216;
  half_gaussian_weight[3] = 0.0540540541;
  half_gaussian_weight[4] = 0.0162162162;

  vec4 sum = texture(inputImageTexture, uv) * half_gaussian_weight[0];

  for (int i = 1; i <= radius; i++) {
    vec2 curRightCoordinate =
        uv + vec2(float(i) * direction.x, float(i) * direction.y) * unit_uv;
    vec2 curLeftCoordinate =
        uv + vec2(float(-i) * direction.x, float(-i) * direction.y) * unit_uv;
    sum += texture(inputImageTexture, curRightCoordinate) *
           half_gaussian_weight[i];
    sum +=
        texture(inputImageTexture, curLeftCoordinate) * half_gaussian_weight[i];
  }

  gl_FragColor = vec4(sum.xxx, sum.w);
}
