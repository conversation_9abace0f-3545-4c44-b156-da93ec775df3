# 关于Beauytkit
## 介绍
Beauytkit(BK)是一套以Lua为开发语言的渲染引擎，支持2D和3D渲染。重要：所有的内建模块都已经自动导入到全局空间，无需手动requeir。
## 版本
v3.2.2

------

# 功能模块
## Node
节点基类，Beauytkit渲染引擎基于节点系统，一切渲染元素都直接或间接继承自Node，运行时有且只有一个引擎根节点，名为gContextWrapper，只有在根节点下的节点会被渲染处理。

### 创建以及销毁节点
#### 关键方法
```lua
---创建节点对象
---@return Node 新的对象
function Node.new() end

---即时销毁自身
function Node:destroy() end
---延时销毁自身
function Node:delayDestroy() end
---无效化自身
function Node:deactivate() end
```

#### 例子
```lua
-- 以下代码实现了一个空白节点的创建以及销毁
globalNode = Node.new()
gContextWrapper:addChild(globalNode) -- 添加到根节点中

globalNode2 = Node.new()
gContextWrapper:addChild(globalNode2) -- 添加到根节点中

-- after a few frames
-- ......

globalNode:destroy() -- 即时销毁自身以释放资源

globalNode2:deactivate() -- 无效化以实现当前帧不被渲染
globalNode2:delayDestroy() -- 延迟一帧销毁自身
```

------

### 设置或获取名字
节点的名字常用于调试，但如果需要使用相对布局或者基于路径查找节点时，那么名字就是必须的了。

#### 关键方法
```lua
---获取节点名字
---@return string 节点名字
function Canvas:getName() end

---设置节点名字
---@param name string { comment = "新的节点名字 不可为null或空串 " }
function Canvas:setName(name) end
```

#### 例子
```lua
local myNode = Node.new()
myNode:setName("myNode1")
print("Who are you? My name is "..myNode:getName())
```

------

### 设置渲染层级
Beautykit的渲染是基于画家算法的，即靠后的节点渲染内容会覆盖前面的，可通过设置渲染层级确定兄弟节点间的渲染顺序。渲染层级数值越大，处理渲染的时机越靠后

#### 关键方法
```lua
---设置节点渲染层级
---@param val integer { comment = "渲染层级，层级值越小，越早被渲染 " }
function Node:setOrder(val) end
```

#### 例子
```lua
local node1 = Node.new()
gContextWrapper:addChild(node1) -- 节点的渲染层级默认为0

local node2 = Node.new()
node2:setOrder(-1)
gContextWrapper:addChild(node2) -- 尽管node2是后创建并添加到根节点的，但因为渲染层级较node1小，因此node2会先于node1被处理渲染
```

------

### 添加子节点
节点想要发挥作用必须加入到渲染树中，可藉由目标父节点添加自身为子节点实现。但注意一个节点不允许同时添加到多个节点树种。
#### 关键方法
```lua
---添加子节点
---@param child Node { comment = "待加入节点对象 非空对象且不在任何节点树中的对象 " }
---@return boolean 成功加入当前节点树则返回true，否则返回false
function Node:addChild(child) end
```

#### 例子
```lua
local child1 = Node.new()
gContextWrapper:addChild(child1) -- 添加到根节点下

local child2 = Node.new()
child1:addChild(child2) -- 添加到新建节点的节点树下成为三级节点

gContextWrapper:addChild(child2) -- 错误！一个节点不允许同时加入到多颗节点树
```

------

### 获取父节点
当节点在某节点树时，可获取其父节点；反之获取到nil。
#### 关键方法
```lua
---获取节点的父节点
---@return Node 父节点对象，可能为空
function Node:getParent() end
```

#### 例子
```lua
local node1 = Node.new()
gContextWrapper:addChild(node1)
print(node1:getParent() == gContextWrapper) -- 打印true，因为node1的父节点就是gContextWrapper

local node2 = Node.new()
print(node2:getParent() == nil) -- 打印true，因为node2尚未添加到任何节点树，因为getParent获取到nil
```

------

### 脱离父节点
当需要移动节点到其他节点树时，引擎没有提供移动方法，而需要通过先脱离后加入变相实现。尤其适用于对象池场景。
#### 关键方法
```lua
---脱离父节点，可重新加入到其他节点树中【重要】慎重调用，此方法不会释放节点内存，后续必须显式调用destroy或者release进行释放，此方法仅适用于特殊场景，如节点迁移、对象池等
---@return Node 自身对象
function Node:deattachFromParent() end

---延迟脱离父节点，可重新加入到其他节点树中【重要】慎重调用，此方法不会释放节点内存，后续必须显式调用destroy或者release进行释放，此方法仅适用于特殊场景，如节点迁移、对象池等
function Node:delayDeattachFromParent() end
```
#### 例子
```lua
local node1 = Node.new()
gContextWrapper:addChild(node1)

local node2 = Node.new()
gContextWrapper:addChild(node2)

-- 将node1移动到node2下
node1:deattachFromParent()
node2:addChild(node1)

-- 如果在节点的回调中脱离父节点，应该使用延迟版本
node1:scheduleUpdate(function(nodeSelf)
    if gSomethingCondition == true then
        nodeSelf:delayDeattachFromParent() -- 延迟一帧脱离，保证安全
    end
end) 
```

------

### 查找子节点
目前查找子节点有两种方法，一是利用findNodeByPath精准查找；二是利用getChildByIndex遍历查找
#### 关键方法
```lua
---通过节点路径查找节点
---@param str string { comment = "可直接传入名字表示查找当前节点的子节点或通过反斜杠实现路径链查找，如node1/node11表示查找子节点node1的子节点node11 " }
---@return Node 查找成功则返回该节点对象，否则返回null
function Canvas:findNodeByPath(str) end

---返回子节点数量
---@return integer 子节点数量
function Node:getChildrenCount() end

---通过索引获取子节点
---@param index integer { comment = "位置索引 [0, getChildrenCount()) " }
---@return Node 指定索引下的子节点，可为null
function Node:getChildByIndex(index) end
```

#### 例子
```lua
-- 遍历查找名字为node1的子节点
function findChildUsingLoop(node)
    for index = 1, node:getChildrenCount() do
        local child = node:getChildByIndex()
        if child:getName() == 'node1' then
            return child
        end
        return nil
    end
end

-- 以当前节点为起点查找直接子节点node1
local targetNode = node:findNodeByPath('node1')
-- 以当前节点为起点查找直接孙子节点node2
targetNode = node:findNodeByPath('node1/node2')
-- 以根节点为起点查找，等价于gContextWrapper:findNodeByPath('node1/node2')
targetNode = node:findNodeByPath('/node1/node2')
```

------

### 销毁所有子节点
用于一次性销毁所有子节点以释放资源
#### 关键方法
```lua
---销毁所有子节点
function Node:clearAllChildren() end
```

#### 例子
```lua
local child1 = Node.new()
gContextWrapper:addChild(child1)

local child2 = Node.new()
gContextWrapper:addChild(child2)

local child3 = Node.new()
gContextWrapper:addChild(child3)

gContextWrapper:clearAllChildren()
child1:setName('child1') -- 错误! 已被销毁，不可再使用
```

------

### 生命周期回调
所有Node节点都有如下生命周期创建、被添加到节点树时、更新、从节点树被移除，销毁。我们可以创建一个直接或间接继承自Node的新类实现这些生命周期回调。
#### 关键方法
```lua
---是否开启脚本生命周期回调（onUpdate以及onDestroy方法），默认为false
---@param flag boolean 开启传入true，否则传入false
function Node:enableLifeCircleCallback(flag) end
end

```lua
TestLifeCircleCallbackClass = class(Node)

function TestLifeCircleCallbackClass:ctor()
    self:enableLifeCircleCallback(true) -- 必须调用此方法，否则onUpdate()以及onDestroy()不会被调用
end

function TestLifeCircleCallbackClass:onCreate()
    -- 对象实例化时被调用
end

function TestLifeCircleCallbackClass:onAddedToNodeTree()
    -- 对象被添加到节点树时被调用
end

function TestLifeCircleCallbackClass:onUpdate()
    -- 对象每帧更新时被调用
end

function TestLifeCircleCallbackClass:onRemovedFromNodeTree()
    -- 对象从节点树被移除时调用
end

function TestLifeCircleCallbackClass:onDestroy() 
    -- 对象准备被销毁时调用
end
```

------

## Node2D
Node2D继承自Node，拥有Node所有能力，并且新增2D节点通用能力，例如锚点，位置，大小，可视性，Transform等等，还有支持灵活高效的Action系统以实现Transfrom动画。

### 创建Node2D对象
#### 关键方法
```lua
---创建Node2D节点对象
---@return Node2D 新的对象
function Node2D.create() end
```

#### 例子
```lua
globalNode2D = Node2D.create() -- 区别于基类Node，创建对象使用create方法，而非new方法
gContextWrapper:addChild(globalNode2D) -- 添加到根节点中
```

------

### 位置
Node2D的位置是以像素为单位并且原点在画布的左下角

#### 关键方法
```lua
---设置节点在父节点坐标系中的位置，坐标原点在默认视角的左下方
---@param position Vector2 { comment = "位置坐标，以像素为单位 " }
function Node2D:setPosition(position) end

---获取当前节点位置坐标
---@param out Vector2 【可选参数】脚本中可传入Vector2对象作为参数接收返回值，可节省大量的临时内存 node2d:getPosition(__gTempVector2)
---@return Vector2 当out参数为nil时，返回节点在父节点坐标空间的位置坐标，以像素为单位 
function Node2D:getPosition(out) end

---单独设置节点在父节点坐标空间中的横坐标
---@param x number { comment = "横坐标 " }
function Node2D:setPositionX(x) end

---获取节点在父节点坐标空间中的横坐标
---@return number 以像素为单位的节点在父节点坐标空间的横坐标
function Node2D:getPositionX() end

---单独设置节点在父节点坐标空间中的纵坐标
---@param y number { comment = "纵坐标 " }
function Node2D:setPositionY(y) end

---获取节点在父节点坐标空间中的纵坐标
---@return number 以像素为单位的节点在父节点坐标空间的纵坐标
function Node2D:getPositionY() end
```

#### 例子
```lua
local node2d = Node2D.create() -- 区别于基类Node，创建对象使用create方法，而非new方法
gContextWrapper:addChild(node2d) -- 添加到根节点中

node2d:setPosition(Vector2.new(100.0, 100.0)) -- 设置坐标为(100.0, 100.0)，起点在左下角
node2d:setPositionX(100.0) -- 可单独设置横坐标
node2d:setPositionY(200.0) -- 也可单独设置纵坐标

-- 以下代码展示动态获取2d节点当前坐标，并增加横坐标以实现节点向右移动的动画
node2d:scheduleUpdate(function(node2dSelf)
    local currentPosition = node2dSelf:getPosition() -- 当前坐标，返回值为Vector2对象
    currentPosition:x(currentPosition:x() + 1.0) -- 将当前坐标横坐标值+1
    node2dSelf:setPosition(currentPosition) -- 更新2d节点坐标以实现向右动画
end)
```

------

### 大小
Node2D的大小是以contentSize来描述，一般Node2D的子类会根据自身的内容动态设置contentSize，但我们也可以主动设置其大小。

### 关键方法
```lua
---设置节点的大小，以像素为单位
---@param contentSize Size { comment = "节点的目标宽高 " }
function Node2D:setContentSize(contentSize) end

---获取节点的大小
---@return Size 节点的宽高大小，以像素为单位
function Node2D:getContentSize() end
```

### 例子
```lua
local node2d = Node2D.create() -- 区别于基类Node，创建对象使用create方法，而非new方法
gContextWrapper:addChild(node2d) -- 添加到根节点中

node2d:setContentSize(Size.new(100.0, 200.0)) -- 设置宽度为100.0， 高度为200.0

-- 以下代码展示动态获取2d节点当前大小，并增加宽度以实现节点横向放大的动画
node2d:scheduleUpdate(function(node2dSelf)
    local contentSize = node2dSelf:getContentSize() -- 当前大小，返回值为Size对象
    contentSize:width(contentSize:width() + 1.0) -- 将当前宽度值+1
    node2dSelf:setContentSize(contentSize) -- 更新2d节点大小以实现横向放大动画
end)
```

------

### 锚点
锚点对于布局Node2D起着至关重要的作用，描点 + 位置 + 大小共同作用才能够准确定位一个2d节点渲染区域。具体来说属性定义了节点的旋转、缩放和定位的基准点。它是一个二维坐标，通常取值范围在(0, 0)到(1, 1)之间：(0, 0)：左下角；(0.5, 0.5)：中心；(1, 1)：右上角。锚点的影响如下：设置节点的锚点后，所有的变换（如移动、旋转等）都是相对于锚点进行的。锚点位置决定了节点在父节点坐标系中的位置。

#### 关键方法
```lua
---设置节点的锚点，影响节点的所有transform逻辑，可以理解为重心、缩放中心，旋转中心等，对于布局节点极其有用，例如当前节点宽高为(100, 100)，我们想节点的位于父节点的右上角，且节点的右上角与父节点的右上角完全重合，我们只需要设置anchorPoint为(1.0, 1.0)，position设置为(parentWidth, parentHeight)即可
---@param anchorPoint Vector2 { comment = "以左下角为原点的位置百分比，如(0.0, 0.0)表示节点的左下角；(1.0, 1.0)表示节点的右上角 " }
function Node2D:setAnchorPoint(anchorPoint) end
```

#### 例子
```lua
-- 以下代码设置锚点为 (0.5, 0.5) 意味着节点的中心将对齐到其父节点的相应位置。
local node2dRoot = Node2D.create()
gContentWrapper:addChild(node2dRoot)
node2dRoot:setPosition(Vector2.new(gContextWidth / 2.0, gContentHeight / 2.0)) -- 设置节点在画布的中心

local node2dChild = Node2D.create()
node2dRoot:addChild(node2dChild)
node2dChild:setAnchorPoint(Vector2.new(0.0, 1.0)) -- 设置节点的左上角为锚点
node2dChild:setPosition(Vector2.new(0.0, 0.0)) -- 由于节点的锚点为左上角，意味着节点的左上角将对齐到其父节点的相应位置，即画布的中心
```

------

### 包围盒
当一个2d节点的位置（Position），大小（ContentSize）以及锚点（AnchorPoint）被确定后，那么节点的包围盒也即可确定，我们可以利用节点的包围盒计算不同节点间的接触关系或者限制节点的移动等等

#### 关键方法
```lua
---获取节点在父节点坐标空间中的包围盒
---@return Rect 基于父节点坐标空间的包围盒信息
function Node2D:getBoundingBox() end

---获取节点在世界坐标空间中的包围盒
---@return Rect 基于世界坐标空间的包围盒信息
function Node2D:getBoundingBoxInScreen() end
```

#### 例子
```lua
-- 以下例子模拟两个2d节点的碰撞检测
local node2d1 = Node2d.create()
gContextWrapper:addChild(node2d1)
node2d1:setContentSize(Size.new(100.0, 100.0))
node2d2:setPosition(Vector2.new(0.0, 0.0))

local node2d2 = Node2d.create()
gContextWrapper:addChild(node2d2)
node2d2:setContentSize(Size.new(100.0, 100.0))
node2d2:setPosition(Vector2.new(gContextWidth, 0.0))

local boundingBox1 = node2d1:getBoundingBox() -- 因为两个节点有相同的父节点，因此可直接使用getBoundingBox而非getBoundingBoxInScreen
local boundingBox2 = node2d2:getBoundingBox()

if boundingBox1:intersects(boundingBox2) then -- Rect对象有intersects方法用于判断与另一个Rect是否存在相交区域
    print('两个节点区域存在相交，判断为发生碰撞！')
end
```

------

### 缩放
Node2D可以通过缩放属性在不改变contentSize的情况下实现渲染区域的放大或减小，甚至可以通过设置负数缩放值，实现翻转效果。注意缩放中心是以锚点为参考的。

#### 关键方法
```lua
---统一设置横纵坐标缩放系数，默认值为1.0
---@param scale number { comment = "横纵坐标缩放系数 " }
function Node2D:setScale(scale) end

---获取当前横纵坐标缩放系数，当且仅当横纵坐标缩放系数相等的情况
---@return number 横纵坐标缩放系数
function Node2D:getScale() end

---设置横坐标缩放系数，默认值为1.0
---@param scaleX number { comment = "缩放系数 任何浮点数均可，当为负数时，可实现翻转效果 " }
function Node2D:setScaleX(scaleX) end

---获取横坐标缩放系数
---@return number 返回当前横坐标缩放系数
function Node2D:getScaleX() end

---设置纵坐标缩放系数，默认值为1.0
---@param scaleY number { comment = "缩放系数 任何浮点数均可，当为负数时，可实现翻转效果 " }
function Node2D:setScaleY(scaleY) end

---获取当前纵坐标缩放系数
---@return number 返回当前纵坐标缩放系数
function Node2D:getScaleY() end
```

#### 例子
```lua
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:setContentSize(Size.new(100.0, 100.0)) -- 设置大小为(100.0, 100.0)
node2d:setScale(2.0) -- 放大两倍

local boundingBox = node2d:getContentSize()
-- 以下将打印width = 200 height = 200，因为节点被放大了两倍，即使contentSize未发生变化，但包围盒会合并缩放值进行计算
print('width = ' .. tostring(boundingBox:width()) .. ' height = ' .. tostring(boundingBox:height()))
```

------

### 旋转
Node2D可以通过rotation属性实现渲染效果，注意rotation属性是以角度为单位的，且旋转点是以锚点为参考的。

#### 关键方法
```lua
---设置节点在父节点坐标空间中的旋转角度
---@param rotation number { comment = "旋转角度，以角度为单位，如[0, 360] " }
function Node2D:setRotation(rotation) end

---获取节点的旋转角度
---@return number 父节点坐标空间中的旋转角度
function Node2D:getRotation() end
```

#### 例子
```lua
-- 以下代码模拟节点以自身中心旋转180度
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:setAnchorPoint(Vector2.new(0.5, 0.5))
node2d:setRotation(180.0)
```

------

### 仿射变换
Node2D的仿射变换（Affine Transform）用于对节点进行平移、旋转和缩放的组合变换。在基于节点坐标的需求中，可获取节点的变换矩阵进行不同坐标空间系的计算。

#### 关键方法
```lua
---设置额外的仿射变换矩阵
---@param additionalTransform Matrix { comment = "仿射变换矩阵 " }
function Node2D:setAdditionalTransform(additionalTransform) end

---获取节点相对于父节点的仿射变换矩阵
---@return Matrix 仿射变换矩阵，以像素为单位【重要】脚本中可传入Matrix对象作为参数接收返回值，可节省大量的临时内存 node2d:getNodeToParentTransform(__gTempMatrix)
function Node2D:getNodeToParentTransform() end

---获取节点相对于父节点的仿射变换矩阵的逆矩阵
---@return Matrix 仿射变换矩阵，以像素为单位【重要】脚本中可传入Matrix对象作为参数接收返回值，可节省大量的临时内存 node2d:getParentToNodeTransform(__gTempMatrix)
function Node2D:getParentToNodeTransform() end

---获取节点相对于世界空间的仿射变换矩阵
---@return Matrix 仿射变换矩阵，以像素为单位【重要】脚本中可传入Matrix对象作为参数接收返回值，可节省大量的临时内存 node2d:getNodeToWorldTransform(__gTempMatrix)
function Node2D:getNodeToWorldTransform() end

---获取节点相对于世界空间的仿射变换矩阵的逆矩阵
---@return Matrix 仿射变换矩阵，以像素为单位【重要】脚本中可传入Matrix对象作为参数接收返回值，可节省大量的临时内存 node2d:getWorldToNodeTransform(__gTempMatrix)
function Node2D:getWorldToNodeTransform() end
```

#### 例子
```lua
-- 以下例子通过一个Matrix平替直接设置节点的位移，旋转以及缩放
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
local lookAtMatrix = Matrix.new()
Matrix.createLookAt(Vector3.new(0.0, 0.0, 0.0), Vector3.new(100.0, 100.0, 100.0), Vector3.new(0.0, 1.0, 0.0), lookAtMatrix)
node2d:setAdditionalTransform(lookAtMatrix)

-- 以下例子计算节点(100.0, 100.0)坐标在世界坐标系下的坐标值
local nodeToWorldTransfrom = node2d:getNodeToWorldTransform()
local checkPoint = Vector3.new(100.0, 100.0, 0.0)
nodeToWorldTransfrom:transformPoint(checkPoint)
print('checkPoint\'s world value is', checkPoint:x(), checkPoint:y(), checkPoint:z()) -- 坐标值从目标节点空间转化为世界坐标空间了
```

------

### 颜色
2D节点通过color属性控制节点的渲染颜色，但颜色属性对于不同类型的2d节点有着截然不同的表现，对于纯Node2D基类，颜色属性无渲染意义，但可以通过isCascadeColorEnabled属性传递颜色值到子节点。

#### 关键方法
```lua
---获取节点颜色（RGB）
---@param color Color3B { comment = "颜色值 " }
function Node2D:setColor(color) end

---获取颜色(RGB)
---@return Color3B 颜色值
function Node2D:getColor() end

---设置是否递归颜色，默认为false，即当本节点修复color时不影响子节点的color
---@param cascadeColorEnabled boolean { comment = "是否允许递归颜色设置 " }
function Node2D:setCascadeColorEnabled(cascadeColorEnabled) end

---是否允许递归颜色RGB
---@return boolean 当前是否允许递归颜色，如是则返回true
function Node2D:isCascadeColorEnabled() end
```

#### 例子
```lua
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:setColor(Color3B.new(255, 0, 0)) -- 设置节点只显示红色通道

node2d:setCascadeColorEnabled(true) -- 设置节点能够传递颜色到子节点(默认为false)

local node2dChild = Node2D.create()
node2d:addChild(node2dChild)
node2dChild:setColor(Color3B.new(255, 255, 255)) -- 即使子节点被设置纯白色，但因为父节点开启了颜色传递，因此子节点渲染时的实际颜色值是(255, 0, 0)，计算方式是各颜色通道乘上父节点各颜色通道的归一化颜色值
```

------

### 不透明度
2D节点通过opacity控制节点的透明度，默认为255，即完全不透明，同时还可以通过isCascadeOpacityEnabled属性控制节点的不透明度能否传递到子节点。

#### 关键方法
```lua
---设置节点的不透明度
---@param opacity integer { comment = "不透明度[0, 255] " }
function Node2D:setOpacity(opacity) end

---获取节点当前不透明度
---@return integer 不透明度[0, 255]
function Node2D:getOpacity() end

---设置是否递归不透明度，纯node2d默认为false，即当本节点不透明度修改不会影响子节点的不透明度
---@param cascadeOpacityEnabled boolean { comment = "是否允许递归不透明度 " }
function Node2D:setCascadeOpacityEnabled(cascadeOpacityEnabled) end

---是否允许递归不透明度
---@return boolean 当前是否允许递归不透明度，如是则返回true
function Node2D:isCascadeOpacityEnabled() end
```

#### 例子
```lua
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:setOpacity(math.floor(255 / 2)) -- 设置节点为半透明

node2d:setCascadeOpacityEnabled(true) -- 设置节点能够传递半透明度到子节点

local node2dChild = Node2D.create()
node2d:addChild(node2dChild)
node2dChild:setOpacity(255) -- 即使子节点被设置为完全不透明，但因为父节点开启了不透明度传递，因此子节点渲染时的实际不透明度只有50%（自己的不透明度乘上父节点的不透明度）
```

------

### Action系统
Node2D的Action功能用于在节点上执行各种动画和运动效果。Action是通过继承自Action类的各个子类实现的，这些子类定义了不同的行为，如移动、旋转、缩放等。使用Action的基本步骤如下：
创建Action实例，例如移动、旋转等；将Action添加到节点上；可以使用序列、重复等方式组合多个Action。注意事项：所有Action对象都是一次性的，被执行后不可复用。

```lua
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)

node2d:runAction(MoveTo.create(1000.0, Vector2.new(100.0, 100.0))) -- 启动一个动作使用runAction方法
node2d:stopAllActions() -- 停止所有未执行完成的动作使用stopAllActions方法

local scaleAction = ScaleTo.create(1000.0, 2.0)
scaleAction:setTag(100) -- 所有动作对象均可以设置tag
node2d:runAction(scaleAction) -- 启动带tag动作
node2d:stopAllActionsByTag(100) -- 停止所有带相同tag的动作

local rotateAction = RotateTo.create(2000.0, 180.0)
rotateAction:setFlags(4) -- 所有动作对象均可以设置flags
node2d:runAction(rotateAction) -- 启动带flags动作
node2d:stopActionsByFlags(4) -- 停止所有带相关flags的动作
```

------

#### 单浮点数回调Action
ActionFloat用来在一定的时间内将一个浮点值从一个值逐渐变化到另一个值，并通过回调函数供使用方二次处理。

```lua
---单浮点数回调动作方法原型
---@param duration number 执行时长，以毫秒为单位
---@param from number 起始值
---@param to number 终点至
---@param cb function [[ Callback<void(float)> ]] 带一个float参数的回调函数
---@return ActionFloat 新的动作对象
function ActionFloat.create(duration, from, to, cb) end

-- 以下代码实现3秒内将强度变量从0.0变化到1.0
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)

GLOBAL_INTERSITY = 0.0

node2d:runAction(ActionFloat.create(3000.0, 0.0, 1.0, function(val)
    GLOBAL_INTERSITY = val
    print("current intersity is", GLOBAL_INTERSITY)
end))
```

------

#### 延时Action
DelayAction用于在执行下一个动作之前延迟指定时间的一种动作，常用于控制动画或事件的时序，即总是配合Sequence使用。

```lua
---延时动作方法原型
---@param duration number 延时长度，以毫秒为单位
---@return DelayAction 新的动作对象
function DelayAction.create(duration) end

-- 以下代码实现1秒后隐藏节点
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create({ DelayAction.create(1000.0), Hide.create() }))
```

------

#### 方法调用Action
这类别Action包括CallFunc以及CallFuncN。CallFunc 可用于在动作序列中调用一个指定的函数，而 CallFuncN 则在调用时传递当前节点作为参数，以便在回调函数中使用。常与Sequence配合使用，例如实现动画结束后的回调。

```lua
---方法调用动作方法原型
---@param callback function 无参数无返回值闭包或方法
---@return CallFunc 新的动作对象
function CallFunc.create(callback) end

---带当前节点回调参数方法调用动作方法原型
---@param callback function 带执行节点对象参数，无返回值闭包或方法 例如function(node) ... end
---@return CallFuncN 新的动作对象
function CallFuncN.create(callback) end

-- 以下代码实现节点移动到指定位置后打印‘已到位’
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 0.0)), CallFunc.create(function() print('已到位') end) }))
```

------

#### 贝塞尔曲线移动Action
这类别Action包括BezierBy以及BezierTo。BezierBy用于沿着指定的贝塞尔曲线相对地移动节点，而 BezierTo 则是用于沿着贝塞尔曲线绝对地移动节点到指定位置。

```lua
---基于偏移量进行贝塞尔曲线移动动作方法原型
---@param duration number 动作执行时长，以毫秒为单位
---@param c1 Vector2 贝塞尔曲线控制点1
---@param c2 Vector2 贝塞尔曲线控制点2
---@param offset Vector2 偏移值
---@return BezierBy 新的动作对象
function BezierBy.create(duration, c1, c2, offset)

---基于准确目的点进行贝塞尔曲线移动动作方法原型
---@param duration number 动作执行时长，以毫秒为单位
---@param c1 Vector2 贝塞尔曲线控制点1
---@param c2 Vector2 贝塞尔曲线控制点2
---@param end Vector2 目的点
---@return BezierTo 新的动作对象
function BezierTo.create(duration, c1, c2, end)

-- 以下代码实现1秒内节点沿着贝塞尔曲线移动到(1000.0, 1000.0)
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(BezierTo.create(1000.0, Vector2.new(500.0, 500.0), Vector2.new(750.0, 750.0), Vector2.new(1000.0, 1000.0)))
```

------

#### 样条曲线移动Action
这类别Action包括CardinalSplineBy以及CardinalSplineTo。CardinalSplineBy 用于沿着指定的卡尔丹样条曲线相对地移动节点，而 CardinalSplineTo 则是用于沿着卡尔丹样条曲线绝对地移动节点到指定位置。生成的曲线是必然经过所传入的控制点的。

```lua
---基于卡尔丹样条曲线相对移动动作方法原型
---@param duration number 动画时间，以毫秒为单位
---@param points Vector2数组 样条曲线采样点数组
---@param tension number 张力系数，取值范围为[0.0, 1.0]
function CardinalSplineBy.create(duration, points, tension) end

---基于卡尔丹样条曲线绝对移动动作方法原型
---@param duration number 动画时间，以毫秒为单位
---@param points Vector2数组 样条曲线采样点数组
---@param tension number 拟合度，取值范围为[0.0, 1.0]
function CardinalSplineTo.create(duration, points, tension) end

-- 以下代码实现节点3秒内沿着指定采样点拟合的曲线进行移动
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(CardinalSplineTo.create(3000.0, { Vector2.new(0.0, 0.0), Vector2.new(100.0, 100.0), Vector2.new(300.0, 300.0) }, 0.5))
```

------

#### Catmull-Rom曲线移动Action
这类别Action包括CatmullRomBy以及CatmullRomTo。CatmullRomBy 用于沿着指定的卡特穆尔-罗姆样条曲线相对地移动节点，而 CatmullRomTo 则是用于沿着卡特穆尔-罗姆样条曲线绝对地移动节点到指定位置。生成的曲线是必然经过所传入的控制点的。

```lua
---样条曲线相对移动版本方法原型
---@param duration number 动画时间，以毫秒为单位
---@param points Vector2数组 样条曲线采样点数组
---@param tension number 张力系数，取值范围为[0.0, 1.0]
---@return CatmullRomBy 新的动作对象
function CatmullRomBy.create(duration, points) end

---样条曲线绝对坐标版本方法原型
---@param duration number 动画时间，以毫秒为单位
---@param points Vector2数组 样条曲线采样点数组
---@param tension number 拟合度，取值范围为[0.0, 1.0]
---@return CatmullRomBy 新的动作对象 新的动作对象
function CatmullRomTo.create(duration, points) end

-- 以下例子实现节点在1秒内沿曲线从(0.0, 0.0)运动到(1000.0, 1000.0)
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(CatmullRomTo.create(1000.0, { Vector2.new(0.0, 0.0), Vector2.new(230.0, 320.0), Vector2.new(660.0, 780.0), Vector2.new(1000.0, 1000.0) }))
```

------

#### 跳跃运动Action
这类别Action包括JumpBy以及JumpTo。JumpBy用于相对地将节点移动并产生跳跃效果，而 JumpTo 则是用于绝对地将节点移动到指定位置并产生跳跃效果。

```lua
---基于相对坐标的跳跃动作原型
---@param duration number 执行时长，以毫秒的单位
---@param position Vector2 目标点相对当前坐标偏移值
---@param height number 跳跃高度
---@param jumps 跳跃次数
---@return JumpBy 新的动作对象
function JumpBy.create(duration, position, height, jumps) end

---基于绝对坐标的跳跃动作原型
---@param duration number 执行时长，以毫秒的单位
---@param position Vector2 目标点坐标
---@param height number 跳跃高度
---@param jumps 跳跃次数
---@return JumpTo 新的动作对象
function JumpTo.create(duration, position, height, jumps) end

-- 以下代码实现节点1秒内从当前点跳跃到(300.0, 0.0)，且最大跳跃高度为100，跳跃次数为2次
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(JumpTo.create(1000.0, Vector2.new(300.0, 0.0), 100.0, 2))
```

------

#### 直线移动Action
这类别Action包括MoveTo以及MoveBy。MoveTo 用于将节点绝对地移动到指定位置，而 MoveBy 则是用于相对地移动节点到当前位置的偏移量。

```lua
---相对移动动作方法原型
---@param duration number 执行时长，以毫秒为单位
---@param deltaPosition Vector2 基于当前位置相对移动距离
---@oaram MoveBy 新的动作对象
function MoveBy.create(duration, deltaPosition) end


---指定目标点移动动作方法原型
---@param duration number 执行时长，以毫秒为单位
---@param position Vector2 目标坐标
---@oaram MoveTo 新的动作对象
function MoveTo.create(duration, position) end

-- 以下代码实现节点在3秒内从当前位置移动到(1000.0, 1000.0)
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(MoveTo.create(3000.0, Vector2.new(1000.0, 1000.0)))
```

------

#### 闪烁Action
Blink用于使节点在指定时间内闪烁（出现和消失）特效的动作。

```lua
---闪烁动作方法原型
---@param duration 执行时长，以毫秒为单位
---@param blinks 闪烁次数
---@return Blink 新的动作对象
function Blink.create(duration, blinks) end

-- 以下代码实现节点在1秒内闪烁10次
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Blink.create(1000.0, 10))
```

------

#### 定点放置Action
Place 用于立即将节点放置到指定位置的动作，不包含任何过渡效果。

```lua
---定点放置动作方法原型
---@param pos Vector2 目标点坐标
---@return Place 新的动作对象
function Place.create(pos)

-- 以下代码实现节点被放置在(1000.0, 1000.0)的位置
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Place.create(Vector2.new(1000.0, 1000.0)))
```

------

#### 透明度渐变Action《包含渐入和渐出两个特殊Action》
这类别Action包括FadeTo、FadeIn以及FadeOut。FadeTo 是用于在指定时间内将节点的透明度渐变到指定值，FadeIn 是用于将节点从完全透明逐渐显示到不透明，FadeOut 则是用于将节点从不透明逐渐消失到完全透明。

```lua
---指定不透明度渐变方法原型
---@param duration number 执行时长，以毫秒为单位
---@param opacity number 不透明度，取值范围为[0, 255]
---@return FadeTo 新的动作对象
function FadeTo.create(duration, opacity) end

---渐变到完全透明方法原型
---@param duration number 执行时长，以毫秒为单位
---@return FadeOut 新的动作对象
function FadeOut.create(duration) end

---渐变到完全不透明方法原型
---@param duration number 执行时长，以毫秒为单位
---@return FadeIn 新的动作对象
function FadeIn.create(duration) end

--以下代码实现节点1秒内从完全透明渐变到完全不透明，然后等待2秒后，再次1秒内变为完全透明，并释放自身资源，模拟一个一次性动画的渐入渐出的场景
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:setOpacity(0) -- 首先设置节点初始为完全透明，这样才能实现渐变入场效果
node2d:runAction(Sequence.create({ FadeIn.create(1000.0), DelayTime.create(2000.0), FadeOut.create(1000.0), RemoveSelf.create() }))
```

------

#### 翻转Action
这类别Action包括FlipX以及FlipY。FlipX 用于沿 X 轴翻转节点的动作，FlipY 则是用于沿 Y 轴翻转节点的动作。

```lua
---横向翻转动作方法原型
---@param flag 若要横向翻转则传入true，否则传入false
---@return FlipX 新的动作对象
function FlipX.create(flag) end

---纵向翻转动作方法原型
---@param flag 若要纵向翻转则传入true，否则传入false
---@return FlipY 新的动作对象
function FlipY.create(flag) end

-- 以下代码实现节点1秒后水平方向翻转，再过1秒后垂直方向翻转
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create(DelayTime.create(1000.0), FlipX.create(), DelayTime.create(1000.0), FlipY.create()))
```

------

#### 可见性Action《Hide以及Show》
这类别Action包括ToggleVisibility、Hide以及Show。ToggleVisibility 用于切换节点的可见性（显示或隐藏），Hide 用于将节点设置为不可见，Show 则用于将节点设置为可见。

```lua
---隐藏节点动作方法原型
---@return Hide 新的动作对象
function Hide.create() end

---显示节点动作方法原型
---@return Show 新的动作对象
function Show.create() end

---可见性切换动作方法原型
---@return ToggleVisibility 新的动作对象
function ToggleVisibility.create() end

-- 以下代码实现节点首先被隐藏，经过一秒后再次显示
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create({ Hide.create(), DelayTime.create(1000.0), Show.create() }))
```

------

#### 销毁自身Action
RemoveSelf中用于在动作完成后自动从父节点中移除自并释放对象的动作。

```lua
---销毁自身动作方法原型
---@return RemoveSelf 新的动作对象
function RemoveSelf.create() end

-- 以下代码实现节点1秒内移动到(1000.0, 1000.0)位置，停留2秒后销毁自己
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 1000.0)), DelayTime.create(2000.0), RemoveSelf.create() }))
```

------

#### 重复执行Action《Repeat以及RepeatForever》
这类别Action包括Repeat以及RepeatForever。Repeat 用于重复指定动作指定次数的动作，而 RepeatForever 用于无限次重复指定的动作。

```lua
---有限次数重复方法原型
---@param finiteTimeAction Action 有限时长或者瞬时动作对象
---@param times number 重复次数
---@return Repeat 新的动作对象
function Repeat.create(finiteTimeAction, times) end

---无限重复方法原型
---@param action Action 有限时长的动作对象
---@return RepeatForever 新的动作对象
function RepeatForever.create(action) end

-- 以下代码实现节点旋转3圈
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Repeat.create(RotateBy.create(1000.0, 360.0), 3))

-- 以下代码实现节点无限从(0.0, 0.0)到(1000.0, 0.0)往复运动
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(RepeatForever.create(Sequence.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 0.0)), MoveTo.create(1000.0, Vector2.new(0.0, 0.0)) })))
```

------

#### 大小设置Action
这类别Action包括ResizeBy以及ResizeTo。ResizeBy 用于根据指定的宽度和高度增量调整节点大小的动作，而 ResizeTo 则是用于将节点大小调整到指定的宽度和高度。

```lua
---基于相对量渐变大小方法原型
---@param duration number 动画时长，以毫秒为单位
---@param deltaSize Vector2 变化值，x分量表示宽度，y分量表示高度
---@return ResizeBy 新的动作对象
function ResizeBy.create(duration, deltaSize) end

---基于准确目标值渐变大小方法原型
---@param duration number 动画时长，以毫秒为单位
---@param targetSize Vector2 目标值，x分量表示宽度，y分量表示高度
---@return ResizeTo 新的动作对象
function ResizeTo.create(duration, targetSize) end

-- 以下代码实现节点在1秒内从(0.0, 0.0)变大到(1000.0, 1000.0)
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(ResizeTo.create(1000.0, Vector2.new(1000.0, 1000.0)))
```

------

#### 旋转Action
这类别Action包括RotateBy以及RotateTo。RotateBy 用于根据指定的角度增量旋转节点的动作，而 RotateTo 则是用于将节点旋转到指定的绝对角度。

```lua
---基于相对值旋转动作方法原型
---@param duration number 动画时长，以毫秒为单位
---@param deltaAngle number 相对旋转值，以角度为单位
---@return RotateBy 新的动作对象
function RotateBy.create(duration, deltaAngle) end

---基于准确目标值旋转动作方法原型
---@param duration number 动画时长，以毫秒为单位
---@param targetAngle number 目标旋转值，以角度为单位
---@return RotateTo 新的动作对象
function RotateTo.create(duration, targetAngle) end
```

------

#### 缩放Action
这类别Action包括ScaleBy以及ScaleTo。ScaleBy 用于根据指定的比例因子增量缩放节点的动作，而 ScaleTo 则是用于将节点缩放到指定的绝对比例。

```lua
---基于相对值渐变缩放动作方法原型
---@param duration number 动画时长，以毫秒为单位
---@param sx number x坐标轴相对缩放值
---@param sy number y坐标轴相对缩放值 
---@return ScaleBy 新的动作对象
function ScaleBy.createWithXY(duration, sx, sy) end

---基于精确值渐变缩放动作方法原型
---@param duration number 动画时长，以毫秒为单位
---@param sx number x坐标轴目标缩放值
---@param sy number y坐标轴目标缩放值
---@return ScaleTo 新的动作对象
function ScaleTo.createWithXY(duration, sx, sy) end

-- 以下代码实现节点1秒内将宽度放大两倍
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(ScaleTo.createWithXY(1000.0, 2.0, 2.0)) -- 或者ScaleBy.createWithXY(1000.0, 1.0, 1.0)
```

------

#### 速度设置Action
Speed用于调整动作执行速度的动作调节器，可以使动作加速或减速。

```lua
---调节速度动作方法原型
---@param action Action 动作对象
---@param speed number 速度系数
---@return Speed 新的动作对象
function Speed.create(action, speed) end

-- 以下代码通过Speed将原本2秒放大两倍的动作加速到1秒完成
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Speed.create(ScaleTo.create(2000.0, 2.0, 2.0), 2.0))
```

------

#### 指定其他节点执行Action
TargetedAction 用于将一个动作应用于特定目标，允许在动作执行过程中动态改变目标节点。

```lua
---动态改变动作执行对象方法原型
---@param targetNode Node2D 待执行动作的节点
---@param action Action 待执行动作
---@return TargetedAction 新的对象对象
function TargetedAction.create(targetNode, action) end

-- 以下代码实现节点A移动到(1000.0, 1000.0)后控制节点B放大两倍
local nodeA = Node2D.create()
gContextWrapper:addChild(nodeA)

local nodeB = Node2D.create()
gContextWrapper:addChild(nodeB)

nodeA:runAction(Sequence.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 1000.0)), TargetedAction.create(nodeB, ScaleTo.create(1000.0, 2.0)) }))
```

------

#### 调色Action
这类别Action包括TintBy以及TintTo。TintBy 用于根据指定的颜色增量调整节点的颜色，而 TintTo 则是用于将节点的颜色调整到指定的绝对颜色值。

```lua
---基于相对值调色动作方法原型
---@param duration number 动作时长，以毫秒为单位
---@param deltaRed number 红色分量偏移值，取值范围为[-255, 255]
---@param deltaGreen number 绿色分量偏移值，取值范围为[-255, 255]
---@param deltaBlue number 蓝色分量偏移值，取值范围为[-255, 255]
---@return TintBy 新的动作对象
function TintBy.create(duration, deltaRed, deltaGreen, deltaBlue) end

---基于准确值调色动作方法原型
---@param duration number 动作时长，以毫秒为单位
---@param red number 红色分量，取值范围为[0, 255]
---@param green number 绿色分量，取值范围为[0, 255]
---@param blue number 蓝色分量，取值范围为[0, 255]
---@return TintTo 新的动作对象
function TintTo.create(duration, red, green, blue) end

-- 以下代码实现1秒节点过渡到仅显示红色通道
local node2d = Node2D.create()
gContextWrapper:addChild(node2d) 
node2d:runAction(TintTo.create(1000.0, 255, 0, 0))
```

------

#### 插值函数Action
上述基于时间的Action默认都是基于时间匀速进行的，如果想修改动画速率，可使用插值函数Action进行封装。Beauytkit支持以下插值函数Action。
```lua
---BackIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBackIn插值函数对象
function EaseBackIn.create(action) end
---BackInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBackInOut插值函数对象
function EaseBackInOut.create(action) end
---BackOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBackOut插值函数对象
function EaseBackOut.create(action) end
---BounceIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBounceIn插值函数对象
function EaseBounceIn.create(action) end
---BounceIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBounceIn插值函数对象
function EaseBounceIn.create(action) end
---BounceInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBounceInOut插值函数对象
function EaseBounceInOut.create(action) end
---BounceOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBounceOut
function EaseBounceOut.create(action) end
---CircleActionIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCircleActionIn
function EaseCircleActionIn.create(action) end
---CircleActionInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCircleActionInOut
function EaseCircleActionInOut.create(action) end
---CircleActionOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCircleActionOut
function EaseCircleActionOut.create(action) end
---CubicActionIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCubicActionIn
function EaseCubicActionIn.create(action) end
---CubicActionInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCubicActionInOut
function EaseCubicActionInOut.create(action) end
---CubicActionOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseCubicActionOut
function EaseCubicActionOut.create(action) end
---ElasticIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseElasticIn
function EaseElasticIn.create(action) end
---ElasticInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseElasticInOut
function EaseElasticInOut.create(action) end
---ElasticOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseElasticOut
function EaseElasticOut.create(action) end
---ExponentialIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseExponentialIn
function EaseExponentialIn.create(action) end
---ExponentialInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseExponentialInOut
function EaseExponentialInOut.create(action) end
---ExponentialOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseExponentialOut
function EaseExponentialOut.create(action) end
---In插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseIn
function EaseIn.create(action) end
---InOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseInOut
function EaseInOut.create(action) end
---Out插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseOut
function EaseOut.create(action) end
---QuadraticActionIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuadraticActionIn
function EaseQuadraticActionIn.create(action) end
---QuadraticActionInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuadraticActionInOut
function EaseQuadraticActionInOut.create(action) end
---QuadraticActionOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuadraticActionOut
function EaseQuadraticActionOut.create(action) end
---QuarticActionIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuarticActionIn
function EaseQuarticActionIn.create(action) end
---QuarticActionInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuarticActionInOut
function EaseQuarticActionInOut.create(action) end
---QuarticActionOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseQuarticActionOut
function EaseQuarticActionOut.create(action) end
---SineIn插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseSineIn
function EaseSineIn.create(action) end
---SineInOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return EaseSineInOut
function EaseSineInOut.create(action) end
---SineOut插值
---@param ActionInterval 必须是基于时间Action对象
---@return SineOut
function SineOut.create(action) end
---BezierAction插值，创建对象后，调用setBezierParamer方法设置贝塞尔参数
---@param ActionInterval 必须是基于时间Action对象
---@return EaseBezierAction
function EaseBezierAction.create(action) end

-- 以下例子展示如何使用插值函数Action创建动感动画
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(EaseIn.create(MoveTo.create(1000.0, Vector2.new(1000.0, 1000.0)))) -- 节点在1秒以先慢后快的速率移动到(1000.0, 1000.0)位置

local node2d2 = Node2D.create()
gContextWrapper:addChild(node2d2)
local bezierAction = EaseBezierAction.create(MoveTo.create(1000.0, Vector2.new(1000.0, 0.0)))
bezierAction:setBezierParamer(0.0, 0.04, 0.0, 0.99)
node2d2:runAction(bezierAction) -- 节点在1秒内以前端超快的速度移动，后端以很慢的速度移动到(1000.0, 0.0)的位置
```

------

#### 序列组合Action
Sequence 用于将多个动作顺序执行的组合动作，依次完成每个动作。序列组合动作的执行时长为所有子动作时长的累加值。

```lua
---序列组合动作方法原型
---@param actions [Action] 动作对象数组
---@return Sequence 新的动作对象
function Sequence.create(actions) end

-- 以下代码实现节点首先在1秒内移动到(1000.0, 1000.0)，停留1秒后，在2秒内放大到2倍，最后销毁自己
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Sequence.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 1000.0)), DelayTime.create(1000.0), ScaleTo.createWithXY(2000.0, 2.0, 2.0), RemoveSelf.create() }))
```

------

#### 并发组合Action
Spawn 用于同时执行多个动作的组合动作，所有动作将在同一时间开始并独立运行。并发组合动作的执行时长为所有子动作中时长最大值。

```lua
---并发组合动作方法原型
---@param actions [Action] 动作对象数组
---@return Spawn 新的动作对象
function Spawn.create(actions) end

-- 以下代码实现节点1秒内移动到(1000.0, 1000.0)的同时放大到2倍
local node2d = Node2D.create()
gContextWrapper:addChild(node2d)
node2d:runAction(Spawn.create({ MoveTo.create(1000.0, Vector2.new(1000.0, 1000.0)), ScaleTo.createWithXY(1000.0, 2.0, 2.0) }))
```

------

## Canvas
Canvas继承自Node2D，拥有Node2D所有能力。Canvas是UIElement系列组件的必要直接或间接父节点，即所有的UIElement系列组件必须处于Canvas树下才能生效处理渲染。因此Canvas自身的功能大部分是为了整个ui场景的布局或渲染优化的，包括自适应分辨率、msaa、文本长度计算工具、手动图集管理等等。

### Canvas自适应分辨率
当业务中场景画布的分辨率比例是固定的，那么强烈建议开启自适应分辨率功能，例如我们设定画布分辨率为1920 x 1080，那么布局场景组件时，可以完全参考UI稿的布局数值来填写，实际运行时，系统会自动根据实际画布大小进行缩放，而文本会动态设置字号保证清晰度。提示：在大部分业务场景中，即使存在4:3到16:9的比例切换，也同样建议使用自适应分辨率，具体场景具体应用。如果确定此功能，还需要覆写系统画布大小变动会调用，让其返回true。

```lua
function onSizeChanged()
    return true -- 返回true要求引擎在画布发生改变时不自动重载特效
end
```

```lua
---内部通过自定义相机实现等比例缩放，在等比例的场景下即为高效实用，轻松实现子节点布局全部使用硬编码也可基本保证效果
---@param flag boolean { comment = "是否开启 " }
---@param designedSize Size { comment = "设计画布大小，需要明确区分canvas本身的contentSize，contentSize还是应该跟随屏幕大小 " }
function Canvas:setEnableAutoScaleContent(flag, designedSize) end

--以下例子模拟在任何16:9分辨率下，图像均位于画布的中心，且比例符合视觉统一
local canvas = Canvas.create() -- 创建ui场景的根节点
gContextWrapper:addChild(canvas)
canvas:setEnableAutoScaleContent(true, Size.new(1920.0, 1080.0)) -- 开启以1920 x 1080为标准的16:9画布自适应分辨率渲染

local image = UIImage.create(gResourceRootPath.."/resources/image.png")
canvas:addChild(image)
image:setAnchorPoint(Vector2.new(0.5, 0.5)) -- 以图像的中心为锚点
image:setPosition(Vector2.new(1920.0 / 2.0, 1080.0 / 2.0)) -- 直接硬编码到画布中心，由于Canvas开启了自适应分辨率，因此不管是192 x 108、960 x 540还是其他16:9分辨率均可保证渲染效果统一

local label = UILabel.create()
canvas:addChild(label)
label:setFontSize(32) -- font size同样支持自适应分辨率，运行时会设定一个合适的font size以保证不模糊
label:setString('Hello Auto Scale Canvas')
```

------

### Canvas抗锯齿渲染
Canvas支持场景全局msaa抗锯齿渲染功能。

```lua
---开启关闭msaa方法原型
---@param flag boolean { comment = "如果想开启多重采样抗锯齿则传入true，关闭则传入false " }
function Canvas:setEnableMsaa(flag) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
canvas:setEnableMsaa(true) -- 开启多重抗锯齿渲染，对于优化多边形绘制效果尤为明显

local shape = UIShape.create()
canvas:addChild(shape)
shape:drawRoundedRect(Vector2.new(0.0, 0.0), Vector2.new(100.0, 100.0), 10.0, 100, Color4F.new(1.0, 1.0, 1.0, 1.0)) -- 在msaa渲染下，圆角表现更加平滑
```

------

### 字符串空间占用计算工具
Canvas除了作为统领UI组件的根节点外，还提供了一些小工具辅助业务开发，例如如下介绍的字符串占用空间计算工具。

```lua
---字符串空间占用计算工具方法原型
---@param str string { comment = "待计算字符串 " }
---@param fontPath string { comment = "ttf源 " }
---@param emojiFontPath string { comment = "emoji源 " }
---@param fontSize integer { comment = "字号大小 " }
---@param enableBold boolean { comment = "是否启用粗体效果 " }
---@param outlineSize integer { comment = "描边大小，以像素为单位 " }
---@param outlineExtendSize integer { comment = "外层描边大小，以像素为单位 " }
---@param useGlowEffect boolean { comment = "是否启动发光效果 " }
---@return Size 字符串占用空间大小，以像素为单位
function Canvas:calculateStringSize(str, fontPath, emojiFontPath, fontSize, enableBold, outlineSize, outlineExtendSize, useGlowEffect) end

--以下代码演示如何不适用UILabel的情况下获得字符串的空间占用情况
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
local stringContentSize = canvas:calculateStringSize('StringContent', gResourceRootPath.."/resources/font.ttf", gResourceRootPath.."/resources/emojiFont.ttf", 32, false, 0, 0, false)
print('StringContent占用空间大小为', stringContentSize:width(), stringContentSize:height())
```

------

### Canvas自定义图集
Canvas系统默认对256x256以下的图片进行自动合并到动态图集，但也提供了手动创建图集的方案，可用于对性能和内存特别敏感的场景。

```lua
---创建一个指定大小的图集
---@param width integer
---@param height integer
---@return integer 图集的id，用于后续其他图集接口
function Canvas:createAtlas(width, height) end

---释放图集资源，但不会释放id
---@param atlasId integer { comment = "图集的id，通过createAtlas获取，不建议删除通过getCurrentAtlas接口获取的图集 " }
function Canvas:releaseAtlas(atlasId) end

---获取当前的动态图集，此图集用于渲染过程中的图像合批，当图集容量不够时，自动创建新的图集；也可手动添加外部图像到此图集中
---@return integer 当前动态图集的id
function Canvas:getCurrentAtlas() end

---添加指定外部图像到目标图集中
---@param atlasId integer { comment = "图集id，通过createAtlas或者getCurrentAtlas得到有效的图集 " }
---@param filePath string { comment = "待添加的图像文件路径 " }
---@param capInsets Rect { comment = "【可选】指定九切图缩放区域，默认为0 " }
---@return boolean 当图集容量不足以容纳新图像，则返回false；反之返回true
function Canvas:addImageToAtlas(atlasId, filePath, capInsets) end

---添加BKCanvasEffect内容到目标图集中
---@param atlasId integer { comment = "图集id，通过createAtlas或者getCurrentAtlas得到有效的图集 " }
---@param canvasImage Node { comment = "BKCanvasEffect对象，可不在节点树中 " }
---@param name string { comment = "图像的后续引用名字，注意保证唯一性 " }
---@param capInsets Rect { comment = "【可选】指定九切图缩放区域，默认为0 " }
---@return boolean 当图集容量不足以容纳新图像，则返回false；反之返回true
function Canvas:addCanvasImageToAtlas(atlasId, canvasImage, name, capInsets) end

--使用场景1: 临时关卡图像资源
local canvas = Canvas.create()
gConextWrapper:addChild(canvas)

local level1Altas = canvas:createAtlas(512, 512)
canvas:addImageToAtlas(level1Altas, "/resources/level1/enemy1.png")
canvas:addImageToAtlas(level1Altas, "/resources/level1/enemy2.png")

local enemy = UIImage.create("/resources/level1/enemy1.png") -- 自动使用上述缓存的图集纹理
enemy:setPosition(Vector2.new(1920.0 / 2.0, 1080.0 / 2.0))
canvas:addChild(enemy)

canvas:releaseAtlas(level1Atlas) -- 关卡结束释放资源

--使用场景2: 动态复杂多边形图集
local altasId = canvas:createAtlas(512, 512)
local dynamicShapeCanvas = BKCanvasEffect.createAsOffscreenTexture("dynamic_shape_canvas", 14, 14)
dynamicShapeCanvas:fillRoundedRect(0.0, 0.0, 14.0, 14.0, 3.0)
canvas:addCanvasImageToAtlas(altasId, dynamicShapeCanvas, "dynamic_shape", Rect.new(6, 6, 2, 2)) -- 作为九切图

local bg = UIImage.create("dynamic_shape", Size.new(128.0, 128.0)) -- 自动使用上述缓存的图集纹理
bg:setPosition(Vector2.new(1920.0 / 2.0, 1080.0 / 2.0))
bg:setRenderMode(UIImageRenderMode.UIIMAGE_RENDERMODE_SLICE9) -- 设置为九切图渲染模式
canvas:addChild(bg)
```

------

## UIElement
UIElement继承自Node2D，拥有Node2D所有能力，并且新增装饰节点，触摸点击交互，坐标命中检测，布局组件相关功能。重要提示：UIElement必须依附于Canvas节点才可正常工作，请保证UIElement节点所处节点树拥有一个Canvas节点。

### UIElement装饰节点
UIElement支持添加装饰节点，所谓装饰节点是指区别于通过addChild方法添加的节点，而是通过addProtectedChild方法添加的节点。装饰节点通常在自动布局组件中使用，例如UILinearLayout，UIListView等等，装饰节点不会参与布局计算中，可以理解为直接悬浮在组件之上。

```lua
---添加独立于节点树的子节点，一般用于特殊装饰渲染
---@param child Node { comment = "额外装饰节点 " }
function UIElement:addProtectedChild(child) end

---清除所有装饰节点
function UIElement:clearAllProtectedChildren() end

-- 以下代码实现UILinearLayout上方覆盖一层半透明遮罩
local canvas = Canvas.create() -- UIElement节点必须依附于Canvas
gContextWrapper:addChild(canvas)

local layoutNode = UILinearLayout.create()
canvas:addChild(layoutNode)

local uielement1 = UIElement.create()
layoutNode:addChild(uielement1) -- 普通子节点
local uielement2 = UIElement.create()
layoutNode:addChild(uielement2) -- 普通子节点，参与横向布局

local cover = UILayer.create(Color4B.new(255, 0, 0, 100), Size.new(300.0, 300.0))
cover:setAnchorPoint(Vector2.new(0.0, 0.0))
layoutNode:addProtectedChild(cover) -- 装饰节点，不允许参与横向布局，直接按照自身的Position，AnchorPoint定位
```

------

### UIElement触摸点击交互
UIElement节点统一新增对触摸点击交互支持，还可以通过设置enabled或者touchEnabled属性动态控制交互开关

```lua
---设置此节点是否生效，常用于按钮等交互组件
---@param enabled boolean { comment = "指定是否生效 " }
function UIElement:setEnabled(enabled) end

---返回当前节点是否处于生效状态
---@return boolean 生效状态，若生效中则返回true，反之返回false
function UIElement:isEnabled() end

---设置组件是否允许响应触摸事件，渲染组件默认为false；交互组件默认为true
---@param enabled boolean { comment = "允许响应触摸事件则传入true，否则传入false " }
function UIElement:setTouchEnabled(enabled) end

---获取组件是否响应触摸事件
---@return boolean 组件若可响应触摸事件则返回true，反之返回false
function UIElement:isTouchEnabled() end

---设置触摸事件回调，只有当setTouchEnabled(trur)才会影响触摸事件，一般交互组件默认为true
---@param listener function(element, touchEventType, localX, localY, flag) { comment = "回调函数(触发组件，触摸事件UITouchEventType，触摸点组件本地横坐标，触摸点本地纵坐标， 额外标识) " }
function UIElement:setTouchEventListener(listener) end

---清理所有触摸事件回调
function UIElement:clearTouchEventListener() end

---设置点击事件回调，只有当setTouchEnabled(trur)才会影响点击事件
---@param listener function(element, localX, localY, flag) { comment = "回调函数(触发组件，触摸点组件本地横坐标，触摸点本地纵坐标，额外标识) " }
function UIElement:setClickEventListener(listener) end

---清理所有点击事件回调
function UIElement:clearClickEventListener() end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local uielement = UIElement.create()
canvas:addChild(uielement) -- UIElement系列组件必须在Canvas树中
uielement:setContentSize(Size.new(100.0, 100.0)) -- 设置组件大小以支持被触摸点击
uielement:setTouchEnabled(true) -- UIElement默认不可被触摸
uielement:setTouchEventListener(function(element, touchEventType, localX, localY, flag)
    if touchEventType == UITouchEventType.UI_TOUCHEVENTTYPE_BEGAN then -- 触摸事件开始，可以理解为手指按下
    elseif touchEventType == touchEventType.UI_TOUCHEVENTTYPE_MOVED then -- 移动事件
    elseif touchEventType == touchEventType.UI_TOUCHEVENTTYPE_ENDED then -- 触摸事件结束，可以理解为手指抬起
    elseif touchEventType == touchEventType.UI_TOUCHEVENTTYPE_CANCELED then -- 触摸事件被取消，可能是突然间组件被设置成不可被触摸等
    end
end)
uielement:setClickEventListener(function(element, localX, localY, flag)
    print('uielement has been clicked') -- 组件被点击
end)

-- 当不再需要监听触摸事件时，可以考虑清除监听回调或者设置setTouchEnabled(false)
uielement:clearTouchEventListener() -- 移除触摸事件监听回调
uielement:clearClickEventListener() -- 移除点击事件监听回调
```

------

### UIElement布局
UIElement新增线性布局以及相对布局系统，但需要配合UILinearLayout或者UIRelativeLayout作为父节点方可生效。

#### 外间隔布局属性
```lua
---设置外部间隔方法原型
---@param left number 左边外部间隔, 以像素为单位
---@param top number 上方外部间隔, 以像素为单位
---@param right number 右边外部间隔, 以像素为单位
---@param bottom number 下方外部间隔, 以像素为单位
function UIElement:setLayoutMargin(left, top, right, bottom) end

-- 以下代码实现两个UIElement横向依次布局，中间间隔5个像素
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL) -- 创建横向布局
canvas:addChild(linearLayout)

local elementA = UIElement.create()
linearLayout:addChild(elementA)
local elementB = UIElement.create()
linearLayout:addChild(elementB)
elementB:setLayoutMargin(5.0, 0.0, 0.0, 0.0) -- 在横向布局父节点的作用下，elementB位于elementA的右边，设置左边外间隔即可实现两个element相距5像素
```

------

#### 布局权重属性
当UIElement的父节点为UILinearLayout线性布局且其大小是确定的，那么子节点可以通过layoutWeight属性实现动态宽度或者高度（取决于UILinearLayout的方向）。对于实现弹簧布局尤为重要。

```lua
---布局权重设置方法原型
---@param value number 权重值，取值范围(0.0, +无穷)
function UIElement:setLayoutWeight(value) end

--以下例子模拟拥有确定大小的组件以及纯动态组件在布局权重影响下的布局
--例子中三个spring一共分配了3分权重(1.0 + 1.0 + 1.0)，而父布局组件减去拥有确切大小的子节点后剩余400.0像素（500.0 - 50.0 - 50.0），因此每个spring最终分配到400.0 * (1.0 / 3.0) = 133.333像素的大小
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(500.0, 100.0)) -- 要使用权重布局，父节点的大小必须是确定的
canvas:addChild(linearLayout)

local spring1 = UIElement.create()
linearLayout:addChild(spring1)
spring1:setLayoutWeight(1.0) -- 设置权重为1.0
local myElementA = UIElement.create()
linearLayout:addChild(myElementA)
myElementA:setContentSize(Size.new(50.0, 50.0)) -- 模拟一般组件，自身有确切的大小
local spring2 = UIElement.create()
linearLayout:addChild(spring2) 
spring:setLayoutWeight(1.0) -- 设置权重为1.0
local myElementB = UIElement.create()
linearLayout:addChild(myElementB)
myElementB:setContentSize(Size.new(50.0, 50.0)) -- 模拟一般组件，自身有确切的大小
local spring3 = UIElement.create()
linearLayout:addChild(spring3)
spring3:setLayoutWeight(1.0) -- 设置权重为1.0
```

#### 线性布局重力属性
当UIElement的父节点为UILinearLayout线性布局时，子节点可以通过设置linearLayoutGravity属性实现在水平或垂直方向上的盈余空间进行位置调整（与父布局的方向相交）。

```lua
---线性布局重力属性设置方法原型
---@param gravity UILinearLayoutGravity 可选UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_CENTER(居中),UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_LEFT_OR_TOP(左对齐或上对齐),UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_RIGHT_OR_BOTTOM(右对齐或下对齐)
function UIElement:setLinearLayoutGravity(gravity) end

--以下例子模拟出第二个UIElement在父布局中实现右对齐
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_VERTICAL) -- 不指定确切大小时，父布局将自动应用能够完全容纳所有子组件的最小contentSize
canvas:addChild(linearLayout)

local biggerElement = UIElement.create()
linearLayout:addChild(biggerElement)
biggerElement:setContentSize(Size.new(100.0, 100.0))

local smallerElement = UIElement.create()
linearLayout:addChild(smallerElement)
smallerElement:setContentSize(Size.new(50.0, 50.0))
smallerElement:setLinearLayoutGravity(UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_RIGHT_OR_BOTTOM)
```

------

#### 相对布局
当UIElement的父节点为UIRelativeLayout相对布局时，子节点可以通过relativeLayoutAlign以及relativeLayoutRelativeTo属性配合实现相对布局，此用法要求父布局必须是大小确定的，不具备UILinearLayout自动计算contentSize的能力。

```lua
---设置相对布局对齐方式方法原型
---@param align UIRelativeAlign 可选值有UIRelativeAlign.UIRELATIVEALIGN_PARENT_TOP_LEFT（相对于父布局的左上角）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_TOP_CENTER_HORIZONTAL（相对于父布局的水平中心）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_TOP_RIGHT（相对于父布局的右上角）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_LEFT_CENTER_VERTICAL（相对于父布局的垂直中心）,UIRelativeAlign.UIRELATIVEALIGN_CENTER_IN_PARENT（相对于父布局的中心）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_RIGHT_CENTER_VERTICAL（相对于父布局的垂直中心水平靠右）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_LEFT_BOTTOM（相对于父布局的左下角）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_BOTTOM_CENTER_HORIZONTAL（相对于父布局的水平中心靠下）,UIRelativeAlign.UIRELATIVEALIGN_PARENT_RIGHT_BOTTOM（相对于父布局的右下角）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_ABOVE_LEFTALIGN（相对于目标的左上方）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_ABOVE_CENTER（相对于目标的正上方中心）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_ABOVE_RIGHTALIGN（相对于目标的右上方）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_LEFT_OF_TOPALIGN（相对于目标的左上角）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_LEFT_OF_CENTER（相对于目标的左侧垂直中心）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_LEFT_OF_BOTTOMALIGN（相对于目标的右下角）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_RIGHT_OF_TOPALIGN（相对于目标的右上角）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_RIGHT_OF_CENTER（相对于目标的右侧垂直中心）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_RIGHT_OF_BOTTOMALIGN（相对于目标的右下角）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_BELOW_LEFTALIGN（相对于目标的左下方）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_BELOW_CENTER（相对于目标的正下方）,UIRelativeAlign.UIRELATIVEALIGN_LOCATION_BELOW_RIGHTALIGN（相对于目标的右下方）
function UIElement:setRelativeLayoutAlign(align) end

---设置相对目标方法原型
---@param brotherName string 拥有相同父节点的兄弟节点名字，如果相对父布局则不需要设置此值
function UIElement:setRelativeLayoutRelativeTo(brotherName) end

--以下例子实现一个节点位于父节点的中心，然后另外一个节点位置上一个节点的正上方
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local relativeLayout = UIRelativeLayout.create(Size.new(500.0, 500.0)) -- 相对布局强烈建议使用确切的父布局大小
canvas:addChild(relativeLayout)

local centerElement = UIElement.create()
relativeLayout:addChild(centerElement)
centerElement:setName('centerElement') -- 主动设置名字以供后续被引用
centerElement:setContentSize(Size.new(100.0, 100.0))
centerElement:setRelativeLayoutAlign(UIRelativeAlign.UIRELATIVEALIGN_CENTER_IN_PARENT) -- 节点位于父布局的中心

local element2 = UIElement.create()
relativeLayout:addChild(element2)
element2:setContentSize(Size.new(50.0, 50.0))
element2:setRelativeLayoutRelativeTo('centerElement') -- 设置相对目标
element2:setRelativeLayoutAlign(UIRelativeAlign.UIRELATIVEALIGN_LOCATION_ABOVE_CENTER) -- 位于目标的正上方
element2:setLayoutMargin(0.0, 0.0, 0.0, 5.0) -- 设置5像素的下间隔实现与目标节点分开
```

------

## 布局组件通用能力
UILinearLayout以及UIRelativeLayout均继承自UIBaseLayout，而UIBaseLayout继承自Node2D，因此UIBaseLayout完全可以作为一个通用2d组件容器来使用。在UIElement布局章节中已经介绍了线性布局以及相对布局的基础用法，下面将介绍其他次要能力，例如背景、强制Layout以及线性布局的内边距，布局翻转等。

### 设置布局组件背景
作为一个容器类组件，设置背景是常见业务，除了常规的使用UIImage外，还可以使用UILayer或UIShape实现复杂背景。

```lua
---设置布局组件的背景组件，该组件将自动跟随宿主组件的大小，且渲染层级最低
---@param bgElement UIElement { comment = "待设置背景组件，建议使用UILayer或UIImage " }
function UIBaseLayout:setBackground(bgElement) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
local baseLayout = UIBaseLayout.create()
canvas:addChild(baseLayout)

-- 作为背景使用时，通常使用九切图渲染模式
local bg = UIImage.create(gResourceRootPath.."/resources/bg.png")
bg:setCapInsets(Rect.new(10, 10, 10, 10)) 
bg:setRenderMode(UIImageRenderMode.UIIMAGE_RENDERMODE_SLICE9)
baseLayout:setBackground(bg)

baseLayout:setContentSize(Size.new(300.0, 300.0)) -- 背景组件自动跟随布局组件的大小
```

------

### 强制要求布局组件重新布局
无论是UILinearLayout还是UIRelativeLayout在创建的那一帧会自动进行布局计算，但后续任何子组件发生变化，除了子组件数量发生变动（如增减子组件），其余情况均不会重新计算布局，此时需要调用invalidLayout触发重新布局计算。

```lua
---要求布局组件重新布局计算方法原型
function UIBaseLayout:invalidLayout() end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local linearLayout = UILinearLayout.create() -- 创建默认的横向线性布局
canvas:addChild(linearLayout)

local elementA = UIElement.create()
linearLayout:addChild(elementA)
elementA:setContentSize(Size.new(300.0, 300.0))
local elementB = UIElement.create()
elementB:setContentSize(Size.new(300.0, 300.0))
linearLayout:addChild(elementB)

postCallable(function()
    elementA:setContentSize(Size.new(400.0, 300.0))
    linearLayout:invalidLayout() -- 运行过程中子组件发生变化，需要强制要求布局组件重新计算布局
end)
```

------

### 线性布局内边距
要实现子组件原理父布局的边界，除了可以单独设置子组件的margin属性外，还可以通过设置padding来实现。

```lua
---UILinearLayout设置内边距方法原型
---@param left number { comment = "左内边距，以像素为单位 " }
---@param top number { comment = "上内边距，以像素为单位 " }
---@param right number { comment = "右内边距，以像素为单位 " }
---@param bottom number { comment = "下内边距，以像素为单位 " }
function UILinearLayout:setPadding(left, top, right, bottom) end

--以下例子实现整个容器上下左右都留有5个单位的空白空间
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local linearLayout = UILinearLayout.create()
canvas:addChild(linearLayout)
linearLayout:setPadding(5.0, 5.0, 5.0, 5.0)
local elementA = UIElement.create()
linearLayout:addChild(elementA)
elementA:setContentSize(Size.new(100.0, 100.0))
local elementB = UIElement.create()
linearLayout:addChild(elementB)
elementB:setContentSize(Size.new(100.0, 100.0))
```

------

### 线性布局翻转布局
默认情况下UILinearLayout在水平方向是从左到右开始布局，在垂直方向是从上到下开始布局，可以通过设置layoutDirectionRevert属性实现翻转。

```lua
---线性布局设置翻转布局方法原型
---@param layoutDirectionRevert boolean 从右往左或从下到上布局传入true，否则传入false
function UILinearLayout:setLayoutDirectionRevert(layoutDirectionRevert) end

--以下例子从视觉上elementB在elementA的左边
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local linearLayout = UILinearLayout.create()
canvas:addChild(linearLayout)
linearLayout:setLayoutDirectionRevert(true)
local elementA = UIElement.create()
linearLayout:addChild(elementA)
elementA:setContentSize(Size.new(100.0, 100.0))
local elementB = UIElement.create()
linearLayout:addChild(elementB)
elementB:setContentSize(Size.new(100.0, 100.0))
```

------

## UIImage
UIImage继承自UIElement，拥有UIElement所有能力，并新增对图像渲染的支持。

### UIImage常规使用方法
UIImage主要有两种创建方法，一种是基于文件/纹理名创建，一种是基于精灵帧对象创建，前者依赖Canvas的自动图集能力，因此只能用于渲染512像素以下最大边的图像，但可借助Canvas的自定义图集突破此限制；后者则是使用完全业务管理的纹理图像，通常用于临时的大纹理或者外部精灵图集。

```lua
---指定图像文件创建组件
---@param imageFile string { comment = "图像文件路径或名字" }
---@param fixedSize Size { comment = "【可选】指定固定大小，常配合特殊缩放模式使用；当传入大小为0时，组件自动设置为图像的原始大小 " }
---@return UIImage 新的图像渲染组件对象
function UIImage.create(imageFile, fixedSize) end

---指定精灵帧创建组件
---@param frame SpriteFrame { comment = "精灵帧对象，不可为空 " }
---@return UIImage 新的图像渲染组件对象
function UIImage.createWithSpriteFrame(frame) end

---获取所设置图像的原始大小
---@return Size 图像的原始大小，以像素为单位
function UIImage:getOriginImageSize() end

-- 以下代码分别实现基于文件创建、基于Canvas自定义图集以及基于外部图集创建图像组件
local canvas = Canvas.create() -- ui渲染组件必要的根节点
gContextWrapper:addChild(canvas)

-- 场景1：直接使用文件路径创建图像组件
local imageA = UIImage.create(gResourceRootPath.."/resources/imageA.png")
canvas:addChild(imageA)
imageA:setPosition(Vector2.new(100.0, 100.0))
local originalImageSize = imageA:getOriginImageSize() -- 可以获取图像原始大小

-- 场景2：使用自定义图集里面的纹理创建图像组件
local dynamicShapeImageName = '/resources/dynamic_shape'
local altasId = canvas:createAtlas(512, 512)
local dynamicShapeCanvas = BKCanvasEffect.createAsOffscreenTexture(dynamicShapeImageName, 14, 14)
dynamicShapeCanvas:fillRoundedRect(0.0, 0.0, 14.0, 14.0, 3.0)
canvas:addCanvasImageToAtlas(altasId, dynamicShapeCanvas, dynamicShapeImageName, Rect.new(6, 6, 2, 2)) -- 作为九切图
local imageB = UIImage.create(dynamicShapeImageName)
canvas:addChild(imageB)
imageB:setPosition(Vector2.new(200.0, 200.0))

-- 场景3：使用外部精灵图集创建图像组件
gSpriteFrameCache = SpriteFrameCache.create() -- 纹理资源被SpriteFrameCache持有，因此要注意SpriteFrameCache的生命周期
gSpriteFrameCache:setTextureCache(gContextWrapper:getTextureCache()) -- 固定写法，使用SpriteFrameCache必须设置gContextWrapper的textureCache
gSpriteFrameCache:addSpriteFramesWithJsonFile(gResourceRootPath.."/resources/atlas.json", false) -- 添加外部图集到缓冲中，第二个参数false表示纹理采用异步加载的方式
local imageC = UIImage.createWithSpriteFrame(gSpriteFrameCache:getSpriteFrameByName("enemyC.png")) -- 直接获取SpriteFrame而非纹理名字
canvas:addChild(imageC)
imageC:setPosition(Vector2.new(300.0, 300.0))
```

------

### UIImage动态设置图像
UIImage除了创建时指定图像外，还支持创建后以多个方式设置图像，还支持预裁剪功能。

```lua
---一般设置图像方法原型
---@param imagePath string { comment = "可为图像路径，在同一Canvas下，同一路径的图像自动复用，且在Canvas生命周期内永久存在；也可设置为手动添加到图集的图像名字（如通过Canvas的addImageToAtlas或addCanvasImageToAtlas） " }
function UIImage:setImage(imagePath) end

---设置图像并且自动裁剪为圆形方法原型
---@param imagePath string { comment = "只支持图像路径，在同一Canvas下，同一路径的图像自动复用，且在Canvas生命周期内永久存在 " }
function UIImage:setImageWithCircleClip(imagePath) end

---设置图像并且自动裁剪为指定参数的圆角矩形方法原型
---@param imagePath string
---@param lt number { comment = "左上圆角的半径，以像素为单位，基于图像原始大小" }
---@param rt number { comment = "右上圆角的半径，以像素为单位，基于图像原始大小" }
---@param rb number { comment = "右下圆角的半径，以像素为单位，基于图像原始大小" }
---@param lb number { comment = "左下圆角的半径，以像素为单位，基于图像原始大小" }
function UIImage:setImageWithRoundCornersClip(imagePath, lt, rt, rb, lb) end

---设置图像并且自动以遮罩图为主进行掩码裁剪方法原型
---@param imagePath string { comment = "只支持图像路径，在同一Canvas下，同一路径的图像自动复用，且在Canvas生命周期内永久存在 " }
---@param maskImagePath string { comment = "支持图像路径，掩码图纯白色为完全显示区域，越接近纯黑的像素，裁剪后对应的图像像素透明度越高 " }
function UIImage:setImageWithMaskClip(imagePath, maskImagePath) end

---直接设置渲染纹理，此方法时指定任何来源的纹理，即不一定为Canvas维护的图像，但需要注意这种的用法可能会导致批处理被打断
---@param texture Texture2D
function UIImage:setTexture(texture) end

---直接设置渲染精灵帧，此方法时指定任何来源的精灵帧，即不一定为Canvas维护的图像，但需要注意这种的用法可能会导致批处理被打断
---@param spriteFrame SpriteFrame
function UIImage:setSpriteFrame(spriteFrame) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

-- 以下例子实现运行1秒后设置图像纹理
local delayImage = UIImage.create()
canvas:addChild(delayImage)
postCallableWithDelay(function()
    delayImage:setImage(gResourceRootPath.."/resources/image.png")
end, 1000.0)

-- 以下例子实现常见的圆形头像渲染
local avatarImage = UIImage.create()
canvas:addChild(avatarImage)
avatarImage:setImageWithCircleClip(gResourceRootPath.."/resources/avatar.png")

-- 以下例子实现将图像预先裁剪成圆角矩形图像
local roundedRectImage = UIImage.create()
canvas:addChild(roundedRectImage)
roundedRectImage:setImageWithRoundCornersClip(gResourceRootPath.."/resources/imageA.png", 5.0, 5.0, 5.0, 5.0) -- 注意半径是基于图像原始大小的，不要超过最低边长的一半

-- 以下例子实现利用预设复杂形状掩码图像对图像进行预裁剪
local maskedImage = UIImage.create()
canvas:addChild(maskedImage)
maskedImage:setImageWithMaskClip(gResourceRootPath.."/resources/imageB.png", gResourceRootPath.."/resources/mask.png")

-- 以下例子实现直接创建SpriteFrame进行图像渲染
local directlyImage = UIImage.create()
canvas:addChild(directlyImage)
directlyImage:setSpriteFrame(SpriteFrame.createWithTexture(Texture2D.createWithFile(gResourceRootPath.."/resources/imageC.png", false)))
```

------

### UIImage渲染模式
UIImage自带多种渲染模式以供多种渲染需求，以下是各种渲染模式的介绍
* UIImageRenderMode.UIIMAGE_RENDERMODE_NOSCALE 完全不考虑contentSize的影响，按照图像原始尺寸进行渲染
* UIImageRenderMode.UIIMAGE_RENDERMODE_FILL 将图像完全按照contentSize进行渲染，可能造成图像的不按比例拉伸，此为默认值
* UIImageRenderMode.UIIMAGE_RENDERMODE_PRESERVE_ASPECT_RATIO 基于contentSize保持图像原始比例进行缩小做留白渲染，保证图像完全展示
* UIImageRenderMode.UIIMAGE_RENDERMODE_PRESERVE_ASPECT_RATIO_AND_FILL 基于contentSize保持图像原始比例进行放大做完全填充渲染，图像可能被裁剪
* UIImageRenderMode.UIIMAGE_RENDERMODE_REPEAT_TILE 自动将图像按原始大小重复渲染直到整个UIImage完全覆盖图像
* UIImageRenderMode.UIIMAGE_RENDERMODE_REPEAT_MIRROR_TILE 自动将图像按原始大小重复间隔镜像渲染直到整个UIImage完全覆盖图像
* UIImageRenderMode.UIIMAGE_RENDERMODE_PARTIALFILL_HORIZONTAL 水平方向局部渲染
* UIImageRenderMode.UIIMAGE_RENDERMODE_PARTIALFILL_VERTICAL 垂直方向局部渲染
* UIImageRenderMode.UIIMAGE_RENDERMODE_PARTIALFILL_RADIAL360 扇形局部渲染
* UIImageRenderMode.UIIMAGE_RENDERMODE_SLICE9 九切图渲染
* UIImageRenderMode.UIIMAGE_RENDERMODE_CUSTOM_POLYGON 自定义网格渲染

```lua
---UIImage设置渲染模式方法原型
---@param mode UIImageRenderMode { comment = "渲染模式 " }
function UIImage:setRenderMode(mode) end

---UIImage设置九宫格渲染时的缩放区域，采用纹理坐标系（即左上角为原点）
---@param insets Rect { comment = "缩放区域 " }
function UIImage:setCapInsets(insets) end

---UIImage设置局部渲染比例方法原型
---@param percent number { comment = "局部绘制百分比[0.0, 1.0] " }
function UIImage:setPartialFillPercent(percent) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

-- UIIMAGE_RENDERMODE_FILL、UIIMAGE_RENDERMODE_PRESERVE_ASPECT_RATIO、UIIMAGE_RENDERMODE_PRESERVE_ASPECT_RATIO_AND_FILL、UIIMAGE_RENDERMODE_REPEAT_TILE、UIIMAGE_RENDERMODE_REPEAT_MIRROR_TILE一般搭配固定contentSize使用
local imageA = UIImage.create(gResourceRootPath.."/resources/imageA.png", Size.new(100.0, 100.0))
canvas:addChild(imageA)
imageA:setRenderMode(UIImageRenderMode.UIIMAGE_RENDERMODE_PRESERVE_ASPECT_RATIO)

-- UIIMAGE_RENDERMODE_SLICE9 需要搭配contentSize以及capInsets使用
local imageB = UIImage.create(gResourceRootPath.."/resources/imageB.9.png", Size.new(200.0, 200.0))
canvas:addChild(imageB)
imageB:setRenderMode(UIImageRenderMode.UIIMAGE_RENDERMODE_SLICE9)
-- imageB:setCapInsets(Rect.new(10.0, 10.0, 10.0, 10.0)) -- 可以手动设置可缩放区域，但如果图像本身就是.9.png则无需手动设置

-- UIIMAGE_RENDERMODE_PARTIALFILL_HORIZONTAL、UIIMAGE_RENDERMODE_PARTIALFILL_VERTICAL、UIIMAGE_RENDERMODE_PARTIALFILL_RADIAL360需要搭配setPartialFillPercent使用
local imageC = UIImage.create(gResourceRootPath.."/resources/imageC.png")
canvas:addChild(imageC)
imageC:setRenderMode(UIImageRenderMode.UIIMAGE_RENDERMODE_PARTIALFILL_HORIZONTAL)
imageC:setPartialFillPercent(0.5) -- 渲染一半的图像
```

------

### UIImage采样方式
UIImage渲染图像是默认采用线性过滤采样方式，可以通过setTextureFilterLinear改变此行为。

```lua
---设置主纹理渲染时是否采用线性过滤采样方法原型
---@param flag boolean { comment = "若采用线性采样模式则传入true，反之传入false，默认为true" }
function UIImage:setTextureFilterLinear(flag) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local image = UIImage.create(gResourceRootPath.."/resources/image.png")
canvas:addChild(image)
image:setTextureFilterLinear(false) -- 采用非线性过滤采样模式，即使用最近邻采样模式保持图像的清晰度，极其适用于像素风格的图像
```

------

### UIImage颜色模式
UIImage默认是纯白色渲染模式，即完整保留输入图像的色彩表现，但可以通过setSolidColor方法修改颜色通道；还可以通过setGradientColor方法实现渐变颜色通道，实现特殊效果。

```lua
---设置为纯色渲染模式方法原型
---@param color Color4B { comment = "颜色值 " }
function UIImage:setSolidColor(color) end

---设置为渐变色渲染模式方法原型
---@param startColor Color4B { comment = "起始颜色 " }
---@param endColor Color4B { comment = "终止颜色 " }
---@param alongVec Vector2 { comment = "【可选】渐变方向，默认为自右向左 " }
function UIImage:setGradientColor(startColor, endColor, alongVec) end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

-- 以下例子实现图像渲染只保留红色和透明通道
local imageA = UIImage.create(gResourceRootPath.."/resources/imageA.png")
canvas:addChild(imageA)
imageA:setSolidColor(Color4B.new(255, 0, 0, 255))

-- 以下例子实现图像渲染通道采用渐变模式，假定传入的纹理为纯白图，可以变相实现渐变纹理
local imageB = UIImage.create(gResourceRootPath.."/resources/white.png")
canvas:addChild(imageB)
imageB:setGradientColor(Color4B.new(255, 0, 0, 255), Color4B.new(0, 255, 255, 255), Vector2.new(1.0, 0.0))
```

------

### UIImage灰度图渲染模式
UIImage是直接支持灰度图渲染模式的，尤为适用于表示某区域不可用的场景。

```lua
---设置灰度图渲染模式方法原型，只有使用非外部Program时有效
---@param flag boolean { comment = "需要灰度渲染时传入true，反正传入false，默认为false " }
function UIImage:setGrayed(flag) end

-- 以下例子实现图像以灰度图的形式进行渲染
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
local grayedImage = UIImage.create(gResourceRootPath.."/resources/image.png")
canvas:addChild(grayedImage)
grayedImage:setGrayed(true)
```

------

### UIImage外部Program或外部Shader（着色器）实现自定义渲染
UIImage除了标准的渲染以及灰度图渲染模式外，如果场景需求很特殊的效果，UIImage支持完全自定义Program渲染，还支持最多一张额外纹理作为输入源。

顶点着色器资源 /resources/shaders/mask_content.bkvert
```glsl
#version 310 es

layout(location=0) in vec3 a_position;
layout(location=1) in vec4 a_color;
layout(location=2) in vec2 a_texCoord;
layout(location=3) in vec2 a_texCoord2;

layout(binding=0) uniform VertUniforms {
    mat4 mvpMatrix;
};

layout(location=0) out vec2 textureCoordinate;
layout(location=1) out vec4 color;
layout(location=2) out vec2 textureCoordinate2;

void main()
{
    gl_Position = mvpMatrix * vec4(a_position, 1.0);
    textureCoordinate = a_texCoord;
    textureCoordinate2 = a_texCoord2;
    color = a_color;
}
```

片段着色器资源 /resources/shaders/mask_content.bkfrag
```glsl
#version 310 es

layout(location=0) in vec2 textureCoordinate;
layout(location=1) in vec4 color;
layout(location=2) in vec2 textureCoordinate2;

layout(binding=0) uniform sampler2D inputImageTexture;
layout(binding=1) uniform sampler2D inputImageTexture2;

layout(location=0) out vec4 gl_FragColor;

void main()
{
    vec4 fillColor = texture(inputImageTexture2, textureCoordinate2)
    float alpha = texture(inputImageTexture, textureCoordinate).a;
    gl_FragColor = color * fillColor * alpha;
}
```

```lua
---设置外部Porgram进行渲染
---@param name string { comment = "Program名字，注意保证唯一性，建议以特效项目名字为前缀 " }
---@param vertexShaderPath string { comment = "顶点着色器路径【重要】路径中不需要带后缀，glsl为纯glsl代码，hlsl为纯hlsl代码，bkvert为elsl310为准的可自动翻译代码 " }
---@param fragmentShaderPath string { comment = "片段着色器路径【重要】路径中不需要带后缀，glsl为纯glsl代码，hlsl为纯hlsl代码，bkvert为elsl310为准的可自动翻译代码 " }
function UIImage:setExternalProgram(name, vertexShaderPath, fragmentShaderPath) end

---直接设置渲染第二精灵帧，此方法时指定任何来源的精灵帧，即不一定为Canvas维护的图像，但需要注意这种的用法可能会导致批处理被打断
---@param spriteFrame SpriteFrame
function UIImage:setSecondarySpriteFrame(spriteFrame) end

---重置Program为默认
function UIImage:resetProgram() end

---清空额外Uniforms
function UIImage:clearAdditionalUniforms() end

---当且仅当使用外部Program时使用，增加额外的顶点着色器Uniform
---@param name string { comment = "Uniform的名字 " }
---@param data LuaUtils::LuaArray<float> { comment = "Uniform数据，【重要】即使只有一个float也需要传入数据 " }
---@param dataCount integer { comment = "一份数据占多少个float，如vector4就占4个float，则此参数传入4 " }
---@param arraySize integer { comment = "【可选】默认为1，即非数组uniform " }
function UIImage:addAdditionalVertexUniform(name, data, dataCount, arraySize) end

---更新额外顶点着色器Uniform
---@param name string { comment = "Uniform名字，必须曾经调用addAdditionalVertexUniform才有效 " }
---@param data LuaUtils::LuaArray<float> { comment = "Uniform待更新数据，容量必须于addAdditionalVertexUniform时完全相同 " }
function UIImage:updateAdditionalVertexUniform(name, data) end

---当且仅当使用外部Program时使用，增加额外的片段着色器Uniform
---@param name string { comment = "Uniform的名字 " }
---@param data LuaUtils::LuaArray<float> { comment = "Uniform数据，【重要】即使只有一个float也需要传入数据 " }
---@param dataCount integer { comment = "一份数据占多少个float，如vector4就占4个float，则此参数传入4 " }
---@param arraySize integer { comment = "【可选】默认为1，即非数组uniform " }
function UIImage:addAdditionalFragmentUniform(name, data, dataCount, arraySize) end

---更新额外片段着色器Uniform
---@param name string { comment = "Uniform名字，必须曾经调用addAdditionalFragmentUniform才有效 " }
---@param data LuaUtils::LuaArray<float> { comment = "Uniform待更新数据，容量必须于addAdditionalFragmentUniform时完全相同 " }
function UIImage:updateAdditionalFragmentUniform(name, data) end

---当且仅当使用外部Program时使用，增加额外的顶点着色器Uniform以int形式存在
---@param name string { comment = "Uniform的名字 " }
---@param data LuaUtils::LuaArray<int> { comment = "Uniform数据，【重要】即使只有一个int也需要传入数据 " }
---@param dataCount integer { comment = "一份数据占多少个int，如int4就占4个int，则此参数传入4 " }
---@param arraySize integer { comment = "【可选】默认为1，即非数组uniform " }
function UIImage:addAdditionalVertexIntUniform(name, data, dataCount, arraySize) end

---更新额外顶点着色器Uniform以int形式存在
---@param name string { comment = "Uniform名字，必须曾经调用addAdditionalVertexIntUniform才有效 " }
---@param data LuaUtils::LuaArray<int> { comment = "Uniform待更新数据，容量必须于addAdditionalVertexIntUniform时完全相同 " }
function UIImage:updateAdditionalVertexIntUniform(name, data) end

---当且仅当使用外部Program时使用，增加额外的片段着色器Uniform以int形式存在
---@param name string { comment = "Uniform的名字 " }
---@param data LuaUtils::LuaArray<int> { comment = "Uniform数据，【重要】即使只有一个int也需要传入数据 " }
---@param dataCount integer { comment = "一份数据占多少个int，如int4就占4个int，则此参数传入4 " }
---@param arraySize integer { comment = "【可选】默认为1，即非数组uniform " }
function UIImage:addAdditionalFragmentIntUniform(name, data, dataCount, arraySize) end

---更新额外片段着色器Uniform以int形式存在
---@param name string { comment = "Uniform名字，必须曾经调用addAdditionalFragmentIntUniform才有效 " }
---@param data LuaUtils::LuaArray<int> { comment = "Uniform待更新数据，容量必须于addAdditionalFragmentIntUniform时完全相同 " }
function UIImage:updateAdditionalFragmentIntUniform(name, data) end

---清除所有的额外纹理
function UIImage:clearAdditionalTextures() end

---当且仅当使用外部Program时使用，增加额外的顶点着色器纹理
---@param name string { comment = "Uniform的名字 " }
---@param texture Texture2D { comment = "纹理对象，不可为空 " }
---@param linearFilter boolean { comment = "采样方式是否采用线性过滤 " }
---@param wrapType UIImageAdditionalTextureWrapType { comment = "纹理坐标越界处理模式可选CLAMP/REPEAT以及MIRROR_REPEAT模式 " }
function UIImage:addAdditionalTextureForVertex(name, texture, linearFilter, wrapType) end

---当且仅当使用外部Program时使用，增加额外的片段着色器纹理
---@param name string { comment = "Uniform的名字 " }
---@param texture Texture2D { comment = "纹理对象，不可为空 " }
---@param linearFilter boolean { comment = "采样方式是否采用线性过滤 " }
---@param wrapType UIImageAdditionalTextureWrapType { comment = "纹理坐标越界处理模式可选CLAMP/REPEAT以及MIRROR_REPEAT模式 " }
function UIImage:addAdditionalTextureForFragment(name, texture, linearFilter, wrapType) end

---当且仅当使用外部Program时使用，更新额外纹理的参数
---@param name string { comment = "Uniform的名字 " }
---@param texture Texture2D { comment = "纹理对象，不可为空 " }
---@param linearFilter boolean { comment = "采样方式是否采用线性过滤 " }
---@param wrapType UIImageAdditionalTextureWrapType { comment = "纹理坐标越界处理模式可选CLAMP/REPEAT以及MIRROR_REPEAT模式 " }
function UIImage:updateAdditionalTextureInfo(name, texture, linearFilter, wrapType) end

-- 以下代码实现复杂自定义掩码渲染
local rt = RenderTargetNode.createWithSize("mainContent", 128, 128) -- 模拟复杂渲染内容
gContextWrapper:addChild(rt)
local mainContentCanvas = Canvas.create()
rt:addChild(mainContentCanvas)

local mainContentImage = UIImage.create("/resources/image1.png")
mainContentCanvas:addChild(mainContentImage)
mainContentImage:setContentSize(Size.new(128.0, 128.0))
mainContentImage:setPosition(Vector2.new(64.0, 64.0))

local displayCanvas = Canvas.create()
gContextWrapper:addChild(displayCanvas)

local finalImage = UIImage.create("/resources/content_mask.png") -- 以掩码图作为主图
displayCanvas:addChild(finalImage)
finalImage:setContentSize(Size.new(128.0, 128.0))
finalImage:setPosition(Vector2.new(width / 2.0, height / 2.0))
finalImage:setSecondarySpriteFrame(SpriteFrame.createWithTexture(rt:getTexture())) -- 以主内容纹理作为第二输入纹理
finalImage:setExternalProgram("mask_content_program", "/resources/shaders/mask_content", "/resources/shaders/mask_content") -- 设置外部着色器名字，顶点着色器以及判断着色器的文件路径，注意不需要带后缀，自动搜索hlsl，glsl，metal或者bkvert以及bkfrag
```

------

## UILabel
UILabel继承自UIElement，拥有UIElement所有的能力。UILabel负责对文本进行渲染，支持TTF、TTC、BMFont以及Emoji Font。

### UILabel基础用法
UILabel最简单的用法就是设置直接调用setString进行文本渲染，UILabel内置简单的ascii字形库，可以直接渲染英文、数字以及常见的符号。但如果需要渲染中文、emoji或其他语言，就需要设置ttfFontSource以及emojiFontSource了；同时也支持图像字形，可以通过bmfFontSource来设置，三种字形来源均可同时设置，如果存在相同字形，优先级为 BMFont > EmojiFont > TTFFont。UILabel控制渲染区域依赖的是labelSize属性，而常规的contentSize是作为计算结果来使用，即不管是换行、文本过长处理方式等一律以labelSize为标准。UILabel能够自动计算contentSize，因此非特别需求下无需主动设置contentSize。

```lua
---创建文本组件方法原型
---@param size Size { comment = "【可选】固定组件大小，默认为(0, 0)，即自动根据文本调整容器大小 " }
---@return UILabel 新的文本组件对象
function UILabel.create(size) end

---设置字模的TTF来源，优先级为低
---@param filePath string { comment = "TTF文件路径，同一个Canvas下，自动复用字模 " }
function UILabel:setTTFFontSource(filePath) end

---设置emoji字模来源
---@param filePath string { comment = "Emoji TTF文件路径，同一个Canvas下，自动复用字模 " }
function UILabel:setEmojiFontSource(filePath) end

---设置图像字模的来源，优先级为高
---@param filePath string { comment = "fnt文件路径，同一个Canvas下，自动复用字模 " }
function UILabel:setBMFontSource(filePath) end

---设置字号大小，大致可以理解为中文字的最大包围正方形的大小
---@param size integer { comment = "字号大小，以像素为单位 " }
function UILabel:setFontSize(size) end

---设置待渲染字符串，必须是utf8编码格式
---@param str string { comment = "utf8字符串 " }
function UILabel:setString(str) end

--以下例子延时UILabel使用框架
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create()
canvas:addChild(label)
label:setFontSize(32) -- 设置字号，默认为24
label:setTTFFontSource(gResourceRootPath.."/resources/SourceHanSansCN-Normal.ttf") -- 可选设置额外字模来源
label:setBMFontSource(gResourceRootPath.."/resources/colorfont.fnt") -- 可选设置图像字模来源，特别适合花里胡哨的花型数字
label:setEmojiFontSource(gResourceRootPath.."/resources/emoji.ttc") -- 可选设置emoji字模来源
label:setString("Hello, 世界！") -- 因为上述代码设置了中文ttf，因此可以渲染中文
label:setPosition(Vector2.new(400.0, 400.0))
```

------

### UILabel换行与文本溢出处理
UILabel的作为文本的容器，其大小由labelSize属性控制，而常规node2d的contentSize字段更多用于表示UILabel占用空间，例如labelSize默认值宽高都是0，表示宽高不做任何限制，通过布局计算后，通过getContentSize方法可以获取文本内容所占真实空间大小。

```lua
---设置文本组件大小，当需要手动设置文本组件大小，尽可能使用此接口，而非setContentSize
---@param width number { comment = "宽度，以像素为单位 " }
---@param height number { comment = "高度，以像素为单位 " }
function UILabel:setLabelSize(width, height) end

---设置是否允许字符到底行宽最大值时自动换行
---@param flag boolean { comment = "如果允许字符到底行宽最大值时自动换行则传入true，反之传入false，默认为true " }
function UILabel:setEnableWrap(flag) end

---设置字符过多时的处理逻辑
---@param overflow UILabelOverflow { comment = "可选不处理UILABEL_OVERFLOW_NONE；裁剪UILABEL_OVERFLOW_CLAMP；自动计算字号大小UILABEL_OVERFLOW_SHRINK；自动调整组件高度UILABEL_OVERFLOW_RESIZE_HEIGHT " }
function UILabel:setOverflow(overflow) end

---设置是否启用省略功能，必须配合裁剪Overflow（UILABEL_OVERFLOW_CLAMP）使用
---@param flag boolean { comment = "若开启省略功能则传入true，否则返回false " }
---@param ellipsisText string { comment = "【可选】省略符号，默认为三点 " }
function UILabel:setEnableEllipsis(flag, ellipsisText) end

--以下例子演示UILabel以省略号的方式处理文本过长的问题
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create(Size.new(200.0, 200.0))
canvas:addChild(label)
label:setLabelSize(200.0, 200.0) -- 除了在创建方法里面直接传入labelSize，还可以动态设置
label:setOverflow(UILabelOverflow.UILABEL_OVERFLOW_CLAMP) -- 使用裁剪的手段处理过长的文本
label:setEnableEllipsis(true, '...') -- 可选增加省略号裁剪功能，当触发裁剪时使用指定的字符作为结尾而非直接忽略字符
label:setString('ABCDABCDABCDABCDABCDABCDABCDABCDABCDABCDABCDABCDABCDABCD') -- 设置超过文本以触发换行、裁剪等逻辑
-- label:setEnableWrap(true) -- 设置文本超过容器宽度后自动换行，默认就为true，无需刻意调用
```

------

### UILabel的对齐方式
当作为容器的UILabel宽高大于实际内容时，可以通过textAlignment属性控制文本的水平以及垂直方向的对齐方式。

```lua
---设置字符对齐方式
---@param horizontalAlign UILabelTextAlignment { comment = "水平方向对齐方式，可选UILABEL_TEXTALIGNMENT_CENTER/UILABEL_TEXTALIGNMENT_LEFT/UILABEL_TEXTALIGNMENT_RIGHT " }
---@param verticalAlign UILabelTextAlignment { comment = "垂直方向对齐方式，可选UILABEL_TEXTALIGNMENT_CENTER/UILABEL_TEXTALIGNMENT_TOP/UILABEL_TEXTALIGNMENT_BOTTOM " }
function UILabel:setTextAlignment(horizontalAlign, verticalAlign) end

--以下例子演示当文本内容小于容器大小时，如何通过设置对齐方法调整文本渲染位置
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create(Size.new(400.0, 400.0)) -- 创建时直接设置labelSize为(400.0, 400.0)
canvas:addChild(label)
label:setString("ABC") -- 文本内容明显少于容器大小
label:setTextAlignment(UILabelTextAlignment.UILABEL_TEXTALIGNMENT_CENTER, UILabelTextAlignment.UILABEL_TEXTALIGNMENT_BOTTOM) -- 设置文本内容水平方向上于容器居中，垂直方向上位于容器底部
```

------

### UILabel行间距与字间距
UILabel通过lineSpacing属性控制行间距，通过additionalKerning属性控制额外的字间距（每个字形库都有自己内置的字间距，因此说是额外的）。

```lua
---设置行间距
---@param lineSpacing number { comment = "行间距，以像素为单位 " }
function UILabel:setLineSpacing(lineSpacing) end

---设置额外的字间距
---@param additionalKerning number { comment = "字间距，以像素为单位 " }
function UILabel:setAdditionalKerning(additionalKerning) end

-- 以下例子演示如何设置行间距以及字间距
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create(Size.new(100.0, 0.0)) -- 只有labelSize的宽不为0才会触发换行
canvas:addChild(label)
label:setString("ADSDJKSFLJDFKLDFJDFJLDFJDLJFJDF") -- 设置长字符串验证行间距
label:setLineSpacing(10.0) -- 设置行间距为10像素
label:setAdditionalKerning(10.0) -- 设置额外的字间距为10像素，可以为负值，实现字与字之间靠近彼此
```

------

### UILabel字体颜色
UILabel没有为字体颜色新增新的方法，而是复用Node2D的setColor以及setOpacity方法。

```lua
---设置文本颜色（RGB）
---@param color Color3B { comment = "颜色值 " }
function UILabel:setColor(color) end

---设置文本的不透明度
---@param opacity integer { comment = "不透明度[0, 255] " }
function UILabel:setOpacity(opacity) end

--以下例子演示将文本设置为半透明的红色
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)
local label = UILabel.create()
canvas:addChild(label)
label:setString('ABCD')
label:setColor(Color3B.new(255, 0, 0)) -- 设置为红色
label:setOpacity(177) -- 设置为半透明
```

------

### UILabel斜体与粗体
UILabel支持斜体与粗体渲染，但基于算法的粗体渲染存在兼容性问题，推荐使用粗体字形库实现粗体渲染。

```lua
---启用粗体效果
function UILabel:enableBold() end

---禁用粗体效果
function UILabel:disableBold() end

---启用斜体效果
function UILabel:enableItalics() end

---禁用斜体效果
function UILabel:disableItalics() end

--以下例子演示将文本以粗体及斜体进行渲染
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create()
canvas:addChild(label)
label:setString("ABCD")
label:enableBold() -- 启用粗体效果
label:enableItalics() -- 启用斜体效果
```

------

### UILabel发光、描边、双层描边以及阴影效果
UILabel内置支持四种特殊效果，分别是发光、描边、双层描边以及阴影。

```lua
---启用发光效果
---@param color Color4B { comment = "发光颜色 " }
function UILabel:enableGlow(color) end

---启用描边效果，支持双层描边
---@param color Color4B { comment = "描边颜色 " }
---@param outlineSize integer { comment = "描边的厚度，以像素为单位 " }
---@param extendColor Color4B { comment = "外层描边颜色，【可选】默认为白色 " }
---@param outlineExtendSize integer { comment = "外层描边的厚度，以像素为单位，【可选】默认为0 " }
function UILabel:enableOutline(color, outlineSize, extendColor, outlineExtendSize) end

---启用阴影效果
---@param color Color4B { comment = "阴影的颜色 " }
---@param offset Vector2 { comment = "阴影的偏移值，水平方向以右为正，垂直方向以上为正 " }
function UILabel:enableShadow(color, offset) end

--以下例子演示如何同时开启三种效果
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local label = UILabel.create()
canvas:addChild(label)
label:setString("ABCD")
label:enableGlow(Color4B.new(255, 0, 0, 177)) -- 启用半透明红色发光效果
label:enableShadow(Color4B.new(255, 255, 255, 177), Vector2.new(2.0, -2.0)) -- 启用阴影效果，实际上是底层重绘一次字形
label:enableOutline(Color4B.new(255, 0, 255, 255), 4.0) -- 启用单层描边
-- label:enableOutline(Color4B.new(255, 0, 255, 255), 4.0, Color4B.new(0, 255, 255, 255), 8.0) -- 启用双层描边，与单层描边互斥，外层描边size必须大于内层size
```

------

## UILayer
UILayer层组件继承于UIElement，拥有UIElement所有能力。UILayer主要用于渲染色块，一般位于节点树的底层作为背景组件使用。除此之外，层组件支持渐变色、纯色边框以及圆角矩形。

### UILayer基础使用框架
一般创建时确定是纯色还是渐变色块，设置contentSize即可，或者作为UIBaseLayout的背景使用，contentSize会自动跟随父布局节点的大小。

```lua
---指定颜色和大小创建色块组件
---@param color Color4B { comment = "【可选】默认为白色 " }
---@param fixedSize Size { comment = "【可选】固定大小，默认为(0, 0) " }
---@return UILayer 
function UILayer.create(color, fixedSize) end

---创建渐变色色块组件
---@param startColor Color4B { comment = "起始颜色 " }
---@param endColor Color4B { comment = "终止颜色 " }
---@param alongVec Vector2 { comment = "【可选】渐变方向，默认为自右向左 " }
---@param fixedSize Size { comment = "【可选】固定大小，默认为(0, 0) " }
---@return UILayer 
function UILayer.createWithGradientColor(startColor, endColor, alongVec, fixedSize) end

-- 以下例子分别演示作为节点树底层背景以及UIBaseLayout的背景
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local layerAsRootNode = UILayer.create(Color4B.new(100, 100, 100, 100)) -- 半透明灰色层组件
canvas:addChild(layerAsRootNode)
layerAsRootNode:setContentSize(Size.new(200.0, 200.0)) -- 层组件必须手动设置大小

local linearLayout = UILinearLayout.create()
canvas:addChild(linearLayout)
local layerAsBackground = UILayer.createWithGradientColor(Color4B.new(255, 255, 255, 255), Color4B.new(255, 0, 0, 255))
linearLayout:setBackground(layerAsBackground) -- 作为UIBaseLayout背景，contentSize自动跟随父节点
```

------

### UILayer带边框渲染
UILayer内置支持边框渲染，但不能和圆角渲染并存。

```lua
---增加或禁用边框渲染，【重要】启用边框渲染后只能是纯色渲染
---@param color Color4B { comment = "边框颜色 " }
---@param borderWidth integer { comment = "大于0为启用边框渲染，等于0位禁用边框 " }
function UILayer:setEnableBorder(color, borderWidth) end

-- 以下例子演示如何对层组件启用边框渲染
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local layerWithBorder = UILayer.create(Color4B.new(255, 255, 255, 255), Size.new(200.0, 200.0)) -- 创建时即指定大小
canvas:addChild(layerWithBorder)
layerWithBorder:setEnableBorder(Color4B.new(255, 0, 0, 255), 4.0) -- 启用4像素宽的红色边框
```

------

### UILayer圆角矩形
UILayer内置支持圆角矩形渲染，但不能和边框渲染并存。

```lua
---启用圆角渲染【重要】启用圆角渲染后只能是纯色渲染
---@param radius integer { comment = "半径，以像素为单位，等于0表示禁用圆角渲染 " }
function UILayer:setEnableRoundedCorners(radius) end

-- 以下例子演示如何对层组件启用圆角渲染
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local layerWithRoundedRect = UILayer.create(Color4B.new(255, 255, 255, 255), Size.new(200.0, 200.0)) -- 创建时即指定大小
canvas:addChild(layerWithRoundedRect)
layerWithRoundedRect:setEnableRoundedCorners(10.0) -- 不能大于层组件最短边的一半
```

------

## UIMask
UIMask继承自UIElement，拥有UIElement所有能力。UIMask用于裁剪自身范围外部的子组件渲染内容，支持模板测试裁剪以及Scissor裁剪，默认以及推荐使用模板测试。Scissor裁剪唯一优势就是性能稍好，但不支持transform以及自适应分辨率，使用场景限制很多；而且模板测试模式还支持自定义模板，灵活性拉满。

### UIMask基础用法
UIMask的最简单用法只需要创建对象后设置contentSize，然后正常添加子组件即可实现裁剪效果。

```lua
---以直接大小创建裁剪容器组件
---@param fixedSize Size { comment = "容器大小，以像素为单位，默认为(0, 0) " }
---@return UIMask 
function UIMask.create(fixedSize) end

-- 以下例子演示UIMask的最直接用法
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local maskRootNode = UIMask.create(Size.new(300.0, 300.0)) -- 创建时即可指定大小，也可后续通过setContentSize动态设置
canvas:addChild(maskRootNode)

local imageOutOfBoundary = UIImage.create(gResourceRootPath.."/resources/imageA.png")
maskRootNode:addChild(imageOutOfBoundary)
imageOutOfBoundary:setPosition(Vector2.new(-100.0, -100.0)) -- 设置一个明显在MaskRootNode范围内的位置，运行时无法看到其图像，因为被裁剪了

local imageInside = UIImage.create(gResourceRootPath.."/resources/imageB.png")
maskRootNode:addChild(imageInside)
imageInside:setPosition(Vector2.new(150.0, 150.0)) -- 设置在容器的中心，保证可见，但如果图像过大，超出容器范围的部分也会被裁剪
```

------

### UIMask自定义测试模板
当UIMask使用模板测试模式是，支持利用UIShape实现完全自定义测试模板形状以实现特殊形状裁剪。

```lua
---设置自定义裁剪模板方法原型
---@param stencilDrawer UIShape { comment = "自定义模板来源，目前只支持UIShape组件，也可设置为nil禁用自定义模板测试 " }
function UIMask:setCustomlizedStencil(stencilDrawer) end

-- 以下例子演示实现UIMask以圆角矩形的形状对内容物进行裁剪
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local maskRootNode = UIMask.create(Size.new(300.0, 300.0)) -- 创建时即指定容器大小
canvas:addChild(maskRootNode)

local maskShape = UIShape.create() -- 创建多边形绘制组件
maskShape:drawSolidRoundedRect(Vector2.new(0.0, 0.0), Vector2.new(300.0, 300.0), 20.0, 100.0, Color4F.new(1.0, 1.0, 1.0, 1.0)) -- 作为模板参考，颜色必须是纯白色，形状的坐标与大小也应与UIMask的大小为标准
maskRootNode:setCustomlizedStencil(maskShape) -- 将UIShape对象传入UIMask的自定义模板中

-- ...... 后续maskRootNode的子组件如果在容器的边缘对角位置，将会看到被裁剪成圆角
```

------

## UIShape
UIShape继承自UIElement，拥有UIElement所有能力。UIShape主要功能是提供了自定义点线面渲染，适用于实时绘制多边形场景。可以配合Canvas的msaa渲染提高渲染效果。

### UIShape基础用法
UIShape基础用法一般流程为创建UIShape对象，调用绘制指令；如果需要每帧变化，还需要调用清空指令clear方法，再调用新的绘制指令。需要注意的是UIShape的绘制指令坐标系是基于父节点的，而非全局坐标系。

```lua
---创建空白的形状渲染组件
---@return UIShape 新的组件对象
function UIShape.create() end

---清理所有绘制路径
function UIShape:clear() end

-- 以下例子演示UIShape静态渲染以及动画渲染
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local staticShape = UIShape.create()
canvas:addChild(staticShape)
staticShape:drawRect(Vector2.new(100.0, 100.0), Vector2.new(300.0, 300.0), Color4F.new(1.0, 0.0, 0.0, 1.0)) -- 红色矩形框
staticShape:drawSolidRectWithBorder(Vector2.new(200.0, 200.0), Vector2.new(400.0, 400.0), Color4F.new(0.0, 0.0, 0.0, 0.0), 5.0, Color4F.new(1.0, 0.0, 1.0, 1.0)) -- 透明填充色+有色边界 变相实现中空有带宽度的边框

gOffset = 0.0
-- 每帧改变圆形位置变相实现移动动画
local dynamicShape = UIShape.create()
canvas:addChild(dynamicShape)
dynamicShape:scheduleCallable(function()
    gOffset = gOffset + 1.0
    dynamicShape:clear() -- 首先清理当前的绘制内容
    dynamicShape:drawSolidCircle(Vector2.new(gOffset, 100.0), 100.0, 0.0, 100.0, Color4F.new(1.0, 0.0, 1.0, 1.0)) -- 模拟一个实心圆移动动画
end)
```

### UIShape所有绘制指令
基于上述UIShape的基础用法，结合下面的所有绘制UIShape的绘制能力，可以实现绝大多数的2d点线面绘制场景。

```lua
---绘制线段
---@param origin Vector2 { comment = "起点，以左下角为原点 " }
---@param destination Vector2 { comment = "终点，以左下角为原点 " }
---@param color Color4F { comment = "颜色 " }
function UIShape:drawLine(origin, destination, color) end

---以对角线方式绘制矩形框
---@param origin Vector2 { comment = "对角线的起点 " }
---@param destination Vector2 { comment = "对角线的终点 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawRect(origin, destination, color) end

---以四角绘制四边形，实际就是相邻两个点进行连线
---@param p1 Vector2 { comment = "点1 " }
---@param p2 Vector2 { comment = "点2 " }
---@param p3 Vector2 { comment = "点3 " }
---@param p4 Vector2 { comment = "点4 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawRectWithCorners(p1, p2, p3, p4, color) end

---填充绘制带边框的矩形
---@param origin Vector2 { comment = "矩形对角线的起点 " }
---@param destination Vector2 { comment = "矩形对角线的终点 " }
---@param color Color4F { comment = "填充颜色 " }
---@param borderWidth number { comment = "边框厚度，必须大于等于1才生效 " }
---@param borderColor Color4F { comment = "边框颜色，透明度必须大于0才生效 " }
function UIShape:drawSolidRectWithBorder(origin, destination, color, borderWidth, borderColor) end

---填充绘制矩形
---@param origin Vector2 { comment = "矩形对角线的起点 " }
---@param destination Vector2 { comment = "矩形对角线的终点 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawSolidRect(origin, destination, color) end

---填充绘制带边框圆角矩形
---@param origin Vector2 { comment = "矩形对角线的起点 " }
---@param destination Vector2 { comment = "矩形对角线的终点 " }
---@param radius number { comment = "圆角半径，以像素为单位 " }
---@param segments integer { comment = "分段数，越多越平滑，但建议圆角半径*4即可，太多分段无明显的平滑改进，如果追求高质量平滑曲线，建议使用BKCanvasEffect " }
---@param color Color4F { comment = "填充颜色 " }
---@param borderWidth number { comment = "边框厚度，必须大于等于1才生效 " }
---@param borderColor Color4F { comment = "边框颜色，透明度必须大于0才生效 " }
function UIShape:drawSolidRoundedRectWithBorder(origin, destination, radius, segments, color, borderWidth, borderColor) end

---填充绘制圆角矩形
---@param origin Vector2 { comment = "矩形对角线的起点 " }
---@param destination Vector2 { comment = "矩形对角线的终点 " }
---@param radius number { comment = "圆角半径，以像素为单位 " }
---@param segments integer { comment = "分段数，越多越平滑，但建议圆角半径*4即可，太多分段无明显的平滑改进，如果追求高质量平滑曲线，建议使用BKCanvasEffect " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawSolidRoundedRect(origin, destination, radius, segments, color) end

---绘制圆角矩形线框
---@param origin Vector2 { comment = "矩形对角线的起点 " }
---@param destination Vector2 { comment = "矩形对角线的终点 " }
---@param radius number { comment = "圆角半径，以像素为单位 " }
---@param segments integer { comment = "分段数，越多越平滑，但建议圆角半径*4即可，太多分段无明显的平滑改进，如果追求高质量平滑曲线，建议使用BKCanvasEffect " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawRoundedRect(origin, destination, radius, segments, color) end

---绘制凸多边形线框
---@param poli LuaUtils::LuaArray<Vector2> { comment = "顶点坐标 " }
---@param closePolygon boolean { comment = "是否自动闭合多边形 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawPoly(poli, closePolygon, color) end

---填充绘制凸多边形
---@param poli LuaUtils::LuaArray<Vector2> { comment = "顶点坐标 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawSolidPoly(poli, color) end

---填充绘制带边框凸多边形
---@param verts LuaUtils::LuaArray<Vector2> { comment = "顶点数据 " }
---@param fillColor Color4F { comment = "填充颜色 " }
---@param borderWidth number { comment = "边框厚度，必须大于等于1才生效 " }
---@param borderColor Color4F { comment = "边框颜色，透明度必须大于0才生效 " }
function UIShape:drawPolygon(verts, fillColor, borderWidth, borderColor) end

---绘制圆形线框
---@param center Vector2 { comment = "圆心坐标 " }
---@param radius number { comment = "半径，以像素为单位 " }
---@param angle number { comment = "扫过的角度，以弧度制为单位 " }
---@param segments integer { comment = "分段数，越多圆弧越平滑 " }
---@param drawLineToCenter boolean { comment = "是否绘制线段到圆心 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawCircle(center, radius, angle, segments, drawLineToCenter, color) end

---填充绘制带边框圆形
---@param center Vector2 { comment = "圆心坐标 " }
---@param radius number { comment = "半径，以像素为单位 " }
---@param angle number { comment = "扫过的角度，以弧度制为单位 " }
---@param segments integer { comment = "分段数，越多圆弧越平滑 " }
---@param scaleX number { comment = "X轴缩放比率，如与Y轴缩放比率不相同，那么可实现椭圆的效果 " }
---@param scaleY number { comment = "Y轴缩放比率，如与X轴缩放比率不相同，那么可实现椭圆的效果 " }
---@param color Color4F { comment = "填充颜色 " }
---@param borderWidth number { comment = "边框厚度，必须大于等于1才生效 " }
---@param borderColor Color4F { comment = "边框颜色，透明度必须大于0才生效 " }
function UIShape:drawSolidCircleWithBorder(center, radius, angle, segments, scaleX, scaleY, color, borderWidth, borderColor) end

---填充绘制圆形
---@param center Vector2 { comment = "圆心坐标 " }
---@param radius number { comment = "半径，以像素为单位 " }
---@param angle number { comment = "扫过的角度，以弧度制为单位 " }
---@param segments integer { comment = "分段数，越多圆弧越平滑 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawSolidCircle(center, radius, angle, segments, color) end

---绘制贝塞尔曲线线框
---@param origin Vector2 { comment = "起点 " }
---@param control Vector2 { comment = "控制点 " }
---@param destination Vector2 { comment = "终点 " }
---@param segments integer { comment = "分段数，越多越平滑 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawQuadBezier(origin, control, destination, segments, color) end

---绘制三次贝塞尔曲线线框
---@param origin Vector2 { comment = "起点 " }
---@param control1 Vector2 { comment = "控制点1 " }
---@param control2 Vector2 { comment = "控制点2 " }
---@param destination Vector2 { comment = "终点 " }
---@param segments integer { comment = "分段数，越多越平滑 " }
---@param color Color4F { comment = "线框颜色 " }
function UIShape:drawCubicBezier(origin, control1, control2, destination, segments, color) end

---待补充描述
---@param points LuaUtils::LuaArray<Vector2>
---@param tension number
---@param segments integer
---@param color Color4F
function UIShape:drawCardinalSpline(points, tension, segments, color) end

---待补充描述
---@param points LuaUtils::LuaArray<Vector2>
---@param segments integer
---@param color Color4F
function UIShape:drawCatmullRom(points, segments, color) end

---绘制实心点
---@param pos Vector2 { comment = "位置 " }
---@param radius number { comment = "半径，影响点的大小 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawDot(pos, radius, color) end

---绘制胶囊体
---@param from Vector2 { comment = "起点 " }
---@param to Vector2 { comment = "终点 " }
---@param radius number { comment = "半径 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawSegment(from, to, radius, color) end

---填充绘制三角形
---@param p1 Vector2 { comment = "点1 " }
---@param p2 Vector2 { comment = "点2 " }
---@param p3 Vector2 { comment = "点3 " }
---@param color Color4F { comment = "填充颜色 " }
function UIShape:drawTriangle(p1, p2, p3, color) end

---填充绘制圆弧
---@param center Vector2 { comment = "圆心 " }
---@param radius number { comment = "半径 " }
---@param startAngle number { comment = "起始角度 " }
---@param endAngle number { comment = "终点角度 " }
---@param segments integer { comment = "分段，越多视觉上越平 " }
---@param width number { comment = "圆弧厚度 " }
---@param color Color4F { comment = "圆弧颜色 " }
function UIShape:drawArc(center, radius, startAngle, endAngle, segments, width, color) end
```

------

## UIButton
UIButton继承自UIElement，拥有UIElement所有能力。UIButton承担了点击事件响应功能，支持自定义三态图像（默认态，被点击态以及不可用态）；支持对文本内容进行简单的自定义，如颜色，字体，对齐方式等。

### UIButton常规用法
UIButton最简单的使用方法是创建对象后，设置clickEventListener，设置一个文本内容，即可完成点击交互场景需求，三态图像都有默认的资源。

```lua
---UIButton创建方法原型
---@param title string 按钮标题文本
---@return UIButton 新的按钮对象
function UIButton.create(title) end

---设置点击事件回调方法原型
---@param listener function(element, localX, localY, flag) { comment = "回调函数(触发组件，触摸点组件本地横坐标，触摸点本地纵坐标，额外标识) " }
function UIButton:setClickEventListener(listener) end

---设置标题文本方法原型
---@param title string
function UIButton:setTitle(title) end

-- 以下例子演示按钮的最简用法
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local button = UIButton.create('Click Me!') -- 创建显示 Click Me!的按钮组件
button:setTitle('Set title from setTtile function') -- 也可以通过setTitle方法动态设置按钮标题
canvas:addChild(button)
button:setClickEventListener(function(element, localX, localY, flag)
    print('Button has been clicked!')
end)
```

------

### UIButton自定义三态图
UIButton默认提供了一套三态图（正常，按下，不可用），但也支持自定义图像，且可设置图像是否采用九切图渲染方式。

```lua
---UIButton设置常态图像方法原型
---@param normalImagePath string 图像路径
function UIButton:setNormalImage(normalImagePath) end

---UIButton设置按下态图像方法原型
---@param pressedImagePath string 图像路径
function UIButton:setPressedImage(pressedImagePath) end

---UIButton设置不可用态图像方法原型
---@param disabledImagePath string 图像路径
function UIButton:setDisabledImage(disabledImagePath) end

---UIButton设置三态图是否采用九切图渲染模式方法原型
---@param flag boolean 如果采用九切图渲染模式传入true，否则传入false，默认状态下开启
function UIButton:setEnableImagesScale9Render(flag) end

---UIButton设置三态图可缩放区域，仅在isEnableImagesScale9Render为true时可用
---@param capInsets Rect 可缩放区域，以图像坐标为标准，而非组件坐标空间
function UIButton:setCapInsets(capInsets) end

-- 以下例子演示自定义三态图的按钮
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local button = UIButton.create('Click Me!') -- 创建显示 Click Me!的按钮组件
button:setTitle('Set title from setTtile function') -- 也可以通过setTitle方法动态设置按钮标题
canvas:addChild(button)
button:setNormalImage(gResourceRootPath.."/resources/btn_normal.png")
button:setPressedImage(gResourceRootPath.."/resources/btn_pressed.png")
button:setDisabledImage(gResourceRootPath.."/resources/btn_disabled.png")
button:setCapInsets(Rect.new(10.0, 10.0, 10.0, 10.0)) -- 设置图像的偏移(10, 10)且大小为10 x 10的区域为可缩放区域
button:setClickEventListener(function(element, localX, localY, flag)
    print('Button has been clicked!')
end)
```

------

### UIButton自定义标题样式
UIButton默认使用内置的英文字体渲染白色的标题，而引擎支持修改标题的颜色，字形库，字体大小，对齐方式等，还支持针对不可用态所使用的标题颜色。

```lua
---UIButton修改标题字形库方法原型
---@param fontPath string 普通字形库路径
---@param emojiFontPath string emoji字形库路径，可为空
function UIButton:setTitleFont(fontPath, emojiFontPath) end

---UIButton设置标题字号方法原型
---@param size integer 目标字号大小，以像素为单位
function UIButton:setTitleFontSize(size) end

---UIButton设置常态/按下态标题颜色方法原型
---@param color Color3B 目标颜色，默认值为(255, 255, 255)
function UIButton:setTitleColor(color) end

---UIButton设置不可用态标题颜色方法原型
---@param color Color3B 目标颜色，默认值为(132, 150, 179)
function UIButton:setTitleDisableColor(color) end

---UIButton设置标题对齐方式
---@param horizontalAlignment UILabelTextAlignment 默认值为水平居中，可选UILABEL_TEXTALIGNMENT_CENTER/UILABEL_TEXTALIGNMENT_LEFT/UILABEL_TEXTALIGNMENT_RIGHT
---@param verticalAlignment UILabelTextAlignment 默认值为垂直居中，可选UILABEL_TEXTALIGNMENT_CENTER/UILABEL_TEXTALIGNMENT_TOP/UILABEL_TEXTALIGNMENT_BOTTOM
function UIButton:setTitleAlignment(horizontalAlignment, verticalAlignment) end

-- 以下例子演示UIButton设置全套标题样式
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local button = UIButton.create('Click Me!')
canvas:addChild(button)
button:setTitleFont(gResourceRootPath.."/resources/SourceHanSansCN-Normal.ttf", "") -- 不设置emoji字形库
button:setTitleColor(Color3B.new(255, 0, 0)) -- 设置常态/按下态标题颜色为红色
button:setTitleDisableColor(Color3B.new(0, 255, 0)) -- 设置不可用态标题颜色为蓝色
button:setTitleAlignment(UILabelTextAlignment.UILABEL_TEXTALIGNMENT_LEFT, UILabelTextAlignment.UILABEL_TEXTALIGNMENT_BOTTOM) -- 设置标题位于按钮组件的左下角
```

------

## UIRichText
UIRichText继承自UIElement，拥有UIElement所有能力。UIRichText富文本组件丰富了普通文本组件UILabel可渲染内容，增加了对图片以及复杂自定义组件的支持。

### UIRichText常规用法
UIRichText相较UILbael很大的不同是使用类html格式组装内容，例如普通文本使用<font></font>组合成一段文本，<img></img>组合成一张图像内容，并且相较于普通html而言，不支持单一终结标签，例如</br>必须写成<br></br>表示换行。

```lua
---UIRichText设置文本方法原型
---@param value stdstring { comment = "目标文本，支持类hmtl语法（每个标签必须成对出现，例如<br>最后必须跟一个</br>），支持tag如下i, b, font, outline, shadow, glow, font img " }
function UIRichText:setText(value) end

-- 以下例子演示富文本组件的常规用法，实现普通文本+图像+描边文本
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local richText = UIRichText.create()
canvas:addChild(richText)
richText:setText(string.format("<font color='#FF00FF' align='top'>%s</font><img src='%s'></img><br></br><i><outline color='#F0F0F0' size='4'>%s</outline></i>"), 'ADSDSD', '/resources/image1.png', '1232323')
```

------

### UIRichText所有支持的标签
1. 文本
```html
<!-- align 表示文本在当前行的垂直方向布局方式（适用于于文本的高度比行高小的情况）-->
<font color='#FFFFFF' size='20' face='/resources/fonts/MSYH.ttf' emojiFace='/resources/fonts/emoji.ttf' align='top|bottom|middle'></font>
```

2. 图像
```html
<!-- widht 和 height 不写或者为0时表示使用图像的原生大小 -->
<img src='/resources/image1.png' width='0' height='0'></img>
```

3. 斜体文本
```html
<i>斜体文本</i>
```

4. 粗体文本
```html
<b>粗体文本</b>
```

5. 描边文本
```html
<!-- color2和size2是可选的，表示双层描边 -->
<outline color='#FFFFFF' size='2' color2='#FF0FF' size2='4'>描边文本</outline>
```

6. 阴影文本
```html
<shadow color='#000000' offset-x='2' offset-y='-2'>影文本</shadow>
```

7. 发光文本
```html
<glow color='#FF00FF'>发光文本</glow>
```

8. 动画
```html
<!-- width 和 height参数不写或者置0表示使用动画的原生大小 -->
<animation src='/resources/animation.json' fps='30' width='0' height='0'></animation>
```

9. 自定义组件
```html
<!-- name是必须的，且通过调用registerCustomWidgetCreator注册 -->
<customwidget name='mycustomwidget' width='0' height='0' myattr='23232'></customwidget>
```

### UIRichText自定义组件内容
UIRichText除了支持普通的文本、图像渲染外，还支持完全自定义组件，需要通过registerCustomWidgetCreator(name, creatorCallback)注册自定义组件的构建回调，然后在设置内容时使用<customwidget name='' customattr=''></customwidget>标签描述内容，引擎会自动调用创建逻辑并进行布局渲染。

```lua
---UIRichText注册自定义组件构造回调方法原型
---@param name string 自定义组件名字，后续用于customwidget标签，必须唯一
---@param creatorCallback function(widgetName, attributes) 回调中第一个参数为组件的名字，第二个参数为标签中的所有属性，全部为字符串，需要自行转换为合适的数据结构
function UIRichText:registerCustomWidgetCreator(name, creatorCallback) end

-- 以下例子演示如果往富文本中插入一个自定义组件
local canvas = Canvas.create()
addChild(canvas)

local richText = UIRichText.create()
canvas:addChild(richText)

richText:registerCustomWidgetCreator('mycustomwidget', function(widgetName, params)
    local viewWidth = tonumber(params.width) -- 所有的参数都是字符串，需要手动转换
    local viewHeight = tonumber(params.height)
    local container = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(viewWidth, viewHeight)) -- 作为返回的根节点必须有明确的大小
    local label = UILabel.create()
    container:addChild(label)
    label:setFontSize(tonumber(params.titleSize))
    label:setString(params.title)
    label:setLinearLayoutGravity(UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_CENTER)
    return container
end)

richText:setText("<customwidget> name='mycustomwidget' width='100' height='100' titleSize='24' title='HeHe'") -- 所有自定义参数值都是字符串，customwidget标签必须带'name'属性并指向registerCustomWidgetCreator注册的有效名字
```

------

### UIRichText换行与溢出处理
UIRichText同样支持UILabel的换行以及溢出处理(overflow)，而UIRichText也类似地使用dimensions属性描述大小，也非直接使用contentSize。同样支持水平以及垂直方向上设置对齐方式。

```lua
---设置富文本尺寸，注意尺寸有别于contentSize，contentSize表示实际组件大小，而dimension更多作为布局逻辑逻辑尺寸，如用于对齐、裁剪等行为
---@param width number { comment = "目标宽度，必须大于等于0 " }
---@param height number { comment = "目标高度，必须大于等于0 " }
function UIRichText:setDimensions(width, height) end

---设置内容超尺寸行为模式
---@param overflow UIRichTextOverflow 可选值UIRichTextOverflow.UIRICHTEXT_OVERFLOW_NONE/UIRichTextOverflow.UIRICHTEXT_LOVERFLOW_CLAMP/UIRichTextOverflow.UIRICHTEXT_LOVERFLOW_SHRINK/UIRichTextOverflow.UIRICHTEXT_OVERFLOW_RESIZE_HEIGHT
function UIRichText:setOverflow(overflow) end

---设置整体内容横向对齐方法
---@param align UIRichTextContentHAlignment 可选值UIRichTextContentHAlignment.UIRICHTEXT_CONTENT_HALIGNMENT_LEFT/UIRichTextContentHAlignment.UIRICHTEXT_CONTENT_HALIGNMENT_RIGHT/UIRichTextContentHAlignment.UIRICHTEXT_CONTENT_HALIGNMENT_CENTER
function UIRichText:setContentHorizontalAlignment(align) end

---设置整体内容竖向对齐方法
---@param align UIRichTextContentVAlignment 可选值UIRichTextContentVAlignment.UIRICHTEXT_CONTENT_VALIGNMENT_BOTTOM/UIRichTextContentVAlignment.UIRICHTEXT_CONTENT_VALIGNMENT_MIDDLE/UIRichTextContentVAlignment.UIRICHTEXT_CONTENT_VALIGNMENT_TOP
function UIRichText:setContentVerticalAlignment(align) end

-- 以下例子演示富文本长文本交互
local canvas = Canvas.create()
gContext:addChild(canvas)

local richText = UIRichText.create()
canvas:addChild(richText)
richText:setDimensions(300.0, 300.0) -- 设置富文本容器限制在(300.0, 300.0)的大小，可触发换行以及溢出处理
richText:setContentHorizontalAlignment(UIRichTextContentHAlignment.UIRICHTEXT_CONTENT_HALIGNMENT_CENTER) -- 设置内容在水平方向上居中对齐
richText:setOverflow(UIRichTextOverflow.UIRICHTEXT_LOVERFLOW_CLAMP) -- 设置过长的内容直接忽略掉
richText:setText("lskjcljljvldjsgjsdljglsdjkgsdjkgjsdgf<img src='/resources/image.png'></img>jcljdjgvskl;gjsdl;jgsdljgsdfljgvsdljgblsdjgsdljfdsjflasdjfsodjfd")
```

------

## UIScrollView
UIScrollView继承自UIMask，拥有UIMask所有能力。UIScrollView在UIMask的基础上增加了滚动能力，能够通过滚动显示远远大于容器区域的内容。

## UIListView
UIListView继承自UIScrollView，拥有UIScrollView所有能力。UIListView核心是采用数据-视图驱动，通过一系列的回调方法辅助引擎进行布局渲染以及视图的复用。强烈建议配合Canvas自适应分辨率使用。

### UIListView常规用法
UIListView核心是实现onItemModelType、onGetItemCount、onGetItemContentSize、onFillItemData四个回调方法以及注册对应itemType的视图构建逻辑。可以采用继承的方式实现回调方法，或者直接赋值。

```lua
-- 以下例子采用继承的方法进行演示

MyUIListView = class(UIListView)

function MyUIListView:ctor()
	local names = {
		"Alice", "Bob", "Charlie", "David", "Eve", "Frank", "Grace", "Henry"
	}

	self.datas = {} -- 可以内部维护数据
	for i = 0, 1000 do
		local imgPath = string.format("/res/UIListViewRank/portrait/%03d.png", (i % 30) + 1)
		table.insert(self.datas, {
			rank = i + 1,
			img = imgPath,
			title  = names[(i %  30) + 1]
		})
	end

    self:registerItemModelProvider(0, function(itemType) -- 核心：注册项目的视图构建器
        -- 构建器主要任务是预埋组件，到onFillItemData回调时填充具体的数据
        local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(100.0, 75.0)) -- 项目视图的宽高越早确定越好
        linearLayout.rankLabel = UILabel.create()
        linearLayout.rankLabel:setFontSize(24)
        linearLayout.rankLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        linearLayout.portraitImage = UIImage.create()
        linearLayout.portraitImage:setUseCustomSize(true)
        linearLayout.portraitImage:setContentSize(Size.new(20.0, 20.0))
        linearLayout.portraitImage:setLinearLayoutGravity(UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_CENTER)
        linearLayout.nameLabel = UILabel.create()
        linearLayout.nameLabel:setFontSize(24)
        linearLayout.nameLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        return linearLayout
    end)
end

-- 注意回调方法中的index一律以0为起点
function MyUIListView:onItemModelType(index)
	return 0 -- 本例子只有一种项目样式，因此直接返回0即可
end

function MyUIListView:onGetItemCount()
	return #self.datas -- 返回数据层合计数据条数
end

function MyUIListView:onGetItemContentSize(index)
	return Size.new(100, 75) -- 核心：每条项目的空间占用，强烈建议对于横向ListView，项目高度与ListView相等；竖向ListView，项目宽度与ListView相等
end

-- 引擎自动将index对应的view传递onFillItemData此方法，可自行根据构建器的实现逻辑直接填充内容
function MyUIListView:onFillItemData(index, view)
	view.rankLabel:setString(self.datas[index + 1].rank) -- 在registerItemModelProvider构建器中已经赋值了rankLabel，因此可以直接使用
	view.portraitImage:setImage(gResourceRootPath .. self.datas[index + 1].img) -- 在registerItemModelProvider构建器中已经赋值了portraitImage，因此可以直接使用
	view.nameLabel:setString(self.datas[index + 1].title) -- 在registerItemModelProvider构建器中已经赋值了nameLabel，因此可以直接使用
end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local myListView = MyListView.new()
canvas:addChild(myListView)
myListView:setContentSize(Size.new(100, 750)) -- listview必须明确大小，因此其没有自动计算大小能力
myListView:setPosition(Vector2.new(500.0, 500.0))
```

------

### UIListView多种类项目场景
在日常开发中，偶然需要在一个listview中渲染不同类型的项目，此时就需要为各个类型注册不同的构建器，并且需要在onItemModelType回调方法中返回正确的类型值。

```lua
-- 以下例子演示多类型项目UIListView渲染
MyUIListView = class(UIListView)

-- 为项目类型定义常量，增加代码可读性
MyUIListView.ITEMTYPE1 = 0
MyUIListView.ITEMTYPE2 = 1

function MyUIListView:ctor()
	local names = {
		"Alice", "Bob", "Charlie", "David", "Eve", "Frank", "Grace", "Henry"
	}

	self.datas = {} -- 可以内部维护数据
	for i = 0, 1000 do
		local imgPath = string.format("/res/UIListViewRank/portrait/%03d.png", (i % 30) + 1)
		table.insert(self.datas, {
            type = i % 2 == 0 and MyUIListView.ITEMTYPE1 or MyUIListView.ITEMTYPE2 -- 数据层增加类型字段
			rank = i + 1,
			img = imgPath,
			title  = names[(i %  30) + 1]
		})
	end

    self:registerItemModelProvider(MyUIListView.ITEMTYPE1, function(itemType) -- 核心：注册项目的视图构建器
        -- 构建器主要任务是预埋组件，到onFillItemData回调时填充具体的数据
        local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(100.0, 75.0)) -- 项目视图的宽高越早确定越好
        linearLayout.rankLabel = UILabel.create()
        linearLayout.rankLabel:setFontSize(24)
        linearLayout.rankLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        linearLayout.portraitImage = UIImage.create()
        linearLayout.portraitImage:setUseCustomSize(true)
        linearLayout.portraitImage:setContentSize(Size.new(20.0, 20.0))
        linearLayout.portraitImage:setLinearLayoutGravity(UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_CENTER)
        linearLayout.nameLabel = UILabel.create()
        linearLayout.nameLabel:setFontSize(24)
        linearLayout.nameLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        return linearLayout
    end)

    self:registerItemModelProvider(MyUIListView.ITEMTYPE2, function(itemType) -- 为第二个项目类型注册新的构建器
        local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(100.0, 75.0)) -- 项目视图的宽高越早确定越好
        linearLayout.rankLabel = UILabel.create()
        linearLayout.rankLabel:setFontSize(24)
        linearLayout.rankLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        linearLayout.nameLabel = UILabel.create()
        linearLayout.nameLabel:setFontSize(24)
        linearLayout.nameLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        return linearLayout
    end)
end

-- 注意回调方法中的index一律以0为起点
function MyUIListView:onItemModelType(index)
	return self.datas[index + 1].type -- 直接返回数据层的新增字段，也可通过其他手段计算返回
end

function MyUIListView:onGetItemCount()
	return #self.datas -- 返回数据层合计数据条数
end

function MyUIListView:onGetItemContentSize(index)
    return self.datas[index + 1].type == MyUIListView.ITEMTYPE1 and Size.new(100, 75) or Size.new(100, 150.0) -- 不同类型可返回不同的内容大小，不过即使是同类型也是可以返回不同的大小的，视需求设计
end

-- 引擎自动将index对应的view传递onFillItemData此方法，可自行根据构建器的实现逻辑直接填充内容
function MyUIListView:onFillItemData(index, view)
	view.rankLabel:setString(self.datas[index + 1].rank) -- 在registerItemModelProvider构建器中已经赋值了rankLabel，因此可以直接使用
	view.nameLabel:setString(self.datas[index + 1].title) -- 在registerItemModelProvider构建器中已经赋值了nameLabel，因此可以直接使用
    if self.datas[index + 1].type == MyUIListView.ITEMTYPE1 then -- 只有类型1才需要操作portraitImage组件
	    view.portraitImage:setImage(gResourceRootPath .. self.datas[index + 1].img) -- 在registerItemModelProvider构建器中已经赋值了portraitImage，因此可以直接使用
    end
end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local myListView = MyListView.new()
canvas:addChild(myListView)
myListView:setContentSize(Size.new(100, 750)) -- listview必须明确大小，因此其没有自动计算大小能力
myListView:setPosition(Vector2.new(500.0, 500.0))
```

------

### UIListView动态更新数据
UIListView主打的是视图层复用，那么一定伴随着数据的动态更新，最粗暴的方法是数据变动后，调用notifyDataSetChanged()，会触发全体视图重绘，是一种高消耗行为；但如果明确数据的变动逻辑，可通过notifyItemChanged、notifyItemRangeChanged、notifyItemInserted、notifyItemRangeInserted、notifyItemRemoved、notifyItemRangeRemoved进行精准控制，将重绘视图的范围限制到最小，可明显提高性能。

```lua
---触发UIListView整个视图重绘，高消耗调用
function UIListView:notifyDataSetChanged() end

---触发UIListView指定位置重绘
---@param pos integer 以0为起点的索引位置
function UIListView:notifyItemChanged(pos) end

---触发UIListView指定位置有插入数据
---@param pos integer 以0为起点的索引位置，注意如果是尾部新增数据，pos应该是#datas，即有效数据索引的后一位
function UIListView:notifyItemInserted(pos) end

---触发UIListView指定位置删除数据
---@param pos integer 以0为起点的索引位置
function UIListView:notifyItemRemoved(pos) end

---触发UIListView指定范围重绘
---@param pos integer 以0为起点的索引位置
---@param count integer 项目个数
function UIListView:notifyItemRangeChanged(pos, count) end

---触发UIListView指定位置有插入若干数据
---@param pos integer 以0为起点的索引位置，注意如果是尾部新增数据，pos应该是#datas，即有效数据索引的后一位
---@param count integer 项目个数
function UIListView:notifyItemRangeInserted(pos, count) end

---触发UIListView指定范围删除数据
---@param pos integer 以0为起点的索引位置
---@param count integer 项目个数
function UIListView:notifyItemRangeRemoved(pos, count) end

-- 以下例子采用继承的方法进行演示
MyUIListView = class(UIListView)

function MyUIListView:ctor()
	local names = {
		"Alice", "Bob", "Charlie", "David", "Eve", "Frank", "Grace", "Henry", "Ivy"
	}

	self.datas = {} -- 可以内部维护数据
	for i = 0, 1000 do
		local imgPath = string.format("/res/UIListViewRank/portrait/%03d.png", (i % 30) + 1)
		table.insert(self.datas, {
			rank = i + 1,
			img = imgPath,
			title  = names[(i %  30) + 1]
		})
	end

    self:registerItemModelProvider(0, function(itemType) -- 核心：注册项目的视图构建器
        -- 构建器主要任务是预埋组件，到onFillItemData回调时填充具体的数据
        local linearLayout = UILinearLayout.create(UILinearLayoutDirection.UILINEARLAYOUT_DIRECTION_HORIZONTAL, Size.new(100.0, 75.0)) -- 项目视图的宽高越早确定越好
        linearLayout.rankLabel = UILabel.create()
        linearLayout.rankLabel:setFontSize(24)
        linearLayout.rankLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        linearLayout.portraitImage = UIImage.create()
        linearLayout.portraitImage:setUseCustomSize(true)
        linearLayout.portraitImage:setContentSize(Size.new(20.0, 20.0))
        linearLayout.portraitImage:setLinearLayoutGravity(UILinearLayoutGravity.UILINEARLAYOUT_GRAVITY_CENTER)
        linearLayout.nameLabel = UILabel.create()
        linearLayout.nameLabel:setFontSize(24)
        linearLayout.nameLabel:setTTFFontSource(gResourceRootPath.."/resources/font.ttf")
        return linearLayout
    end)
end

-- 注意回调方法中的index一律以0为起点
function MyUIListView:onItemModelType(index)
	return 0 -- 本例子只有一种项目样式，因此直接返回0即可
end

function MyUIListView:onGetItemCount()
	return #self.datas -- 返回数据层合计数据条数
end

function MyUIListView:onGetItemContentSize(index)
	return Size.new(100, 75) -- 核心：每条项目的空间占用，强烈建议对于横向ListView，项目高度与ListView相等；竖向ListView，项目宽度与ListView相等
end

-- 引擎自动将index对应的view传递onFillItemData此方法，可自行根据构建器的实现逻辑直接填充内容
function MyUIListView:onFillItemData(index, view)
	view.rankLabel:setString(self.datas[index + 1].rank) -- 在registerItemModelProvider构建器中已经赋值了rankLabel，因此可以直接使用
	view.portraitImage:setImage(gResourceRootPath .. self.datas[index + 1].img) -- 在registerItemModelProvider构建器中已经赋值了portraitImage，因此可以直接使用
	view.nameLabel:setString(self.datas[index + 1].title) -- 在registerItemModelProvider构建器中已经赋值了nameLabel，因此可以直接使用
end

local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local myListView = MyListView.new()
canvas:addChild(myListView)
myListView:setContentSize(Size.new(100, 750)) -- listview必须明确大小，因此其没有自动计算大小能力
myListView:setPosition(Vector2.new(500.0, 500.0))

-- 以下例子模拟在尾部添加一条数据，并删除头两条数据，最后更新第10条数据
myListView:notifyItemInserted(#myListView.datas) -- 因为lua的数组下标是从1开始，因此此处先notify再实际添加数据项目
table.insert(myListView.datas, {
    rank = 10000
    img = gResourceRootPath.."/resources/rank1.png"
    title  = 'new-data'
})

table.remove(myListView.datas, 1)
table.remove(myListView.datas, 1)
myListView:notifyItemRangeRemoved(0, 2) -- 通知listview头两条数据被删

myListView.datas[10].title = 'updated-title'
myListView:notifyItemChanged(9) -- 再一次注意到uilistview接口所使用的索引与lua索引相差1
```

------

### UIListView程序式控制滑动
UIListView默认通过点触控制滑动，但某些时候我们需要程序控制滑动，例如数据新增一条后，直接滑动到最后。引擎提供了整个系列接口。但是需要注意的是如果在某一帧发生了数据变动，当前帧并不会立马计算出新的布局信息，因此需要借助延迟调用实现功能，以下代码将进行演示

```lua
---要求UIListView滑动到底部
function UIListView:jumpToBottom() end

---要求UIListView滑动到顶部
function UIListView:jumpToTop() end

---要求UIListView滑动到左边界
function UIListView:jumpToLeft() end

---要求UIListView滑动到右边界
function UIListView:jumpToRight() end

-- 以下例子省略数据构建部分，直接演示UIListView如何在修改数据后尽快滚动到新的底部
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local datas = {}
local listView = UIListView.create()
canvas:addChild(listView)

table.insert(datas, { name = 'dsds' })
listView:notifyItemInserted(1) -- 在尾部新增一条数据
listView:runAction(CallFunc.create(function() -- 利用CallFunc动作配合postCallable实现精准的延迟一帧调用
    postCallable(function()
        listView:jumpToBottom()
    end)
end))
```

------

### UIListView磁力定位滑动
默认情况下UIListView被触摸滚动后会以一定的惯性继续滚动，使滚动停止得丝滑，提高体验感，但这种情况下停止的位置不可预测。因此引擎支持一种名为磁力定位滑动的功能，即停止的位置可设置为UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_TOP(完全展示最接近顶部的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_CENTER(将最接近中心区域的项目定位到容器的中心)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_BOTTOM(完全展示最接近底部的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_LEFT(完全展示最接近左边界的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_RIGHT(完全展示最近右边界的项目)

```lua
---设置UIListView磁力定位滑动方法原型
---@param magneticType UIListViewMagneticType 可选UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_TOP(完全展示最接近顶部的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_CENTER(将最接近中心区域的项目定位到容器的中心)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_BOTTOM(完全展示最接近底部的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_LEFT(完全展示最接近左边界的项目)、UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_RIGHT(完全展示最近右边界的项目)。默认为UIListViewMagneticType.UILISTVIEW_MAGNETIC_TYPE_NONE(不启用磁力定位滑动)
function UIListView:setMagneticType(magneticType) end
```

------

## UIAnimationView
UIAnimationView继承自UIImage，拥有UIImage所有能力。UIAnimationView负责对动画进行渲染，支持四种动画格式：图集动画、序列帧动画、gif动画以及webp动画。

### UIAnimationView常规用法
UIAnimationView基本用法是传入动画资源即可自动播放，还可通过设置动画事件监听处理特殊时刻业务，支持设置fps控制动画播放速度。

```lua
---创建动画组件方法原型
---@param animationFile string 动画资源路径，支持图集动画(json), 序列帧动画(xxx_*.png), gif动画以及webp动画
---@param playOnStart boolean 【可选】创建节点后自动开始播放动画，默认为true
---@return UIAnimationView 新的动画播放组件对象
function UIAnimationView.create(animationFile, playOnStart) end

---设置动画源方法原型
---@param animationFile string 动画资源路径，支持图集动画(json), 序列帧动画(xxx_*.png), gif动画以及webp动画
function UIAnimationView:setSource(animationFile) end

---设置动画事件回调，回调函数带两个参数，分别是动画组件本身以及事件类型UIAnimationEventType
---@param listner function(animationView, animationEventType) 其中animationEventType可能值为UIAnimationEventType.UIAnimationEventTypeStart(动画开始事件)/UIAnimationEventType.UIAnimationEventTypeComplete(非循环播放模式下动画播放完毕事件)/UIAnimationEventType.UIAnimationEventTypeEnd(循环播放模式下动画播放完毕事件)/UIAnimationEventType.UIAnimationEventTypeInterrupt(动画被打断事件)
function UIAnimationView:setAnimationEventListener(listner) end

---设置帧率方法原型
---@param fps number { comment = "每秒帧数，默认为60 " }
function UIAnimationView:setFps(fps) end

-- 以下例子演示播放webp动画以及序列帧动画
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local webpAnimation = UIAnimationView.create(gResourceRootPath.."/resources/fire.webp") -- 创建对象时设置动画源
canvas:addChild(webpAnimation)

local sequenceAnimation = UIAnimationView.create() -- 延迟到setSource设置动画源
canvas:addChild(sequenceAnimation)
sequenceAnimation:setFps(24) -- 设置帧率为24帧每秒
sequenceAnimation:setSource(gResourceRootPath.."/resources/test_animation_*.png") -- 注意序列帧动画的命名规则，前缀为任意字符，但*号处为以5位数字表示的帧号，不足位数用0补全，如第1帧的帧号为00000，第11帧的帧号为00010
```

------

### UIAnimationView播放控制
UIAnimationView如果创建UIAnimationView时要求不自动化开始动画播放，那么就需要手动启动动画，还可以暂停，停止，甚至完全自控帧时间。

```lua
---开始或恢复播放动画
function UIAnimationView:play() end

---停止动画，帧时间自动恢复为0
function UIAnimationView:stop() end

---暂停动画，调用play()后从当前帧开始播放
function UIAnimationView:pause() end

---手动设置帧时间
---@param frametime number { comment = "帧时间，毫秒为单位 " }
function UIAnimationView:setFrameTime(frametime) end

---获取当前序列帧动画帧数
---@return integer 帧数
function UIAnimationView:getFrameCount() end

-- 以下例子演示如何对动画播放进行精准控制
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local animationView = UIAnimationView.create(gResourceRootPath.."/resources/animation.json", false) -- 设置一个图集动画并要求不自动开启播放
canvas:addChild(animationView)

-- 延迟3秒后才开始播放动画
postCallableWithDelay(function()
    animationView:play()
end, 3000)

animationView:setFps(1) -- 可以考虑将fps设置为一秒一帧，后续可以通过setFrame精准控制播放帧
animationView:setFrameTime(2000.0) -- 精准控制动画帧到第3帧
```

------

### UIAnimationView超时播放模式设置
UIAnimationView对于播放时长超过动画时长时要求用户自行设置处理模式，目前支持保持第一帧，保持最后一帧，不渲染以及无限循环播放，默认为无限循环播放。

```lua
---设置超时播放行为模式方法原型
---@param behavior UIOvertimeAnimateBehavior 可选值UIOvertimeAnimateBehavior.UIOvertimeAnimateBehaviorKeep(保持在最后一帧)、UIOvertimeAnimateBehavior.UIOvertimeAnimateBehaviorKeepFirstFrame(保持在第一帧)、UIOvertimeAnimateBehavior.UIOvertimeBehaviorNoRender(不渲染)或者UIOvertimeAnimateBehavior.UIOvertimeAnimateBehaviorLoop(循环播放) 
function UIAnimationView:setOvertimeBehavior(behavior) end
```

------

## UISlider
UISlider继承自UIElement，拥有UIElement所有能力。UISlider封装了进度滑动逻辑，支持单向及双向模式，提供默认ui，也支持自定义UI图片。

### UISlider常规用法
UISlider常规用法为创建对象 -> 设置滑动值范围(默认0到100) -> 设置数值变化监听

```lua
---UISlider创建方法原型
---@return UISlider 新的滑块组件对象
function UISlider.create() end

---UISlider设置滑动数值方法原型
---@param min integer 最小值，可为负数，表示双向滑块
---@param max integer 最大值，只能为正数
function UISlider:setValueRange(min, max) end

---UISlider滑动数值变化监听方法原型
---@param listener function(slider, eventType, value) eventType可能的值为UISliderEventType.UISLIDER_EVENTTYPE_VALUE_CHANGED(滑动数值发生变化),UISliderEventType.UISLIDER_EVENTTYPE_VALUE_SLIDERBALL_DOWN(用户按下滑动球),UISliderEventType.UISLIDER_EVENTTYPE_VALUE_SLIDERBALL_UP(用户停止触摸滑动球),UISliderEventType.UISLIDER_EVENTTYPE_VALUE_SLIDERBALL_CANCEL(用户意外停止触摸滑动球)
function UISlider:setSliderEventListener(listener) end

-- 以下例子演示UISlider的常规用法
local canvas = Canvas.create()
gContextWrapper:addChild(canvas)

local slider = UISlider.create()
canvas:addChild(slider)
slider:setValueRange(0, 100) -- 设置滑动数值范围为[0, 100]
slider:setSliderEventListener(function(sliderView, eventType, value)
    if eventType == UISliderEventType.UISLIDER_EVENTTYPE_VALUE_CHANGED then -- 监听到数值发生变化
        print('current value is', value)
    end
end)
```

------

## BKCommonDrawCell
BKCommonDrawCell继承自Node，拥有Node所有能力。BKCommonDrawCell的作用是在不使用UIImage的环境下进行全图自定义着色器绘制。着色器只支持以文件表示的glsl（用于Opengl以及Opengles环境），hlsl（用于D3D11环境）以及metal（用于iOS的Metal环境），还有是Beautykit的通用着色器——通用顶点着色器后缀为bkvert，通用片段着色器后缀为bkfrag，通用着色器在运行时自行判断运行环境翻译为目标语言着色器。

首先编写着色器，以通用着色器为例子

顶点着色器资源 /resources/shaders/blend_with_image.bkvert
```glsl
#version 310 es

layout(location=0) in vec3 a_position;
layout(location=1) in vec4 a_color;
layout(location=2) in vec2 a_texCoord;

layout(binding=0) uniform VertUniforms {
    mat4 mvpMatrix;
};

layout(location=0) out vec2 textureCoordinate;
layout(location=1) out vec4 color;

void main()
{
    gl_Position = mvpMatrix * vec4(a_position, 1.0);
    textureCoordinate = a_texCoord;
    color = a_color;
}
```

片段着色器资源 /resources/shaders/blend_with_image.bkfrag
```glsl
#version 310 es

layout(location=0) in vec2 textureCoordinate;
layout(location=1) in vec4 color;

layout(binding=0) uniform sampler2D inputImageTexture;

layout(location=0) out vec4 gl_FragColor;

layout(binding=0) uniform FragUniforms {
    float alpha;
};

void main()
{
    vec4 fillColor = texture(inputImageTexture, textureCoordinate);
    gl_FragColor = color * fillColor * alpha;
}
```

BKCommonDrawCell标准使用流程：创建BKCommonDrawCell对象 -> 设置着色器资源路径 -> 设置顶点属性 -> 添加Uniform属性 -> 添加输入纹理 -> 设置渲染模式 -> 设置渲染索引（如果是三角形列表渲染模式）-> 设置加载动作 -> 设置额外渲染环境(如混合模式、深度测试、)

```lua
local drawCell = BKCommonDrawCell.new()
drawCell:setProgramUsingShaderFile("/resources/shaders/blend_with_image", "/resources/shaders/blend_with_image") -- 主要着色器资源路径不需要写后缀名，引擎根本渲染环境自动搜索
local a_positionDataArray = { } -- a_position顶点属性数据集
local a_colorDataArray = { } -- a_color顶点属性数据集
local a_texCoordDataArray = {  } -- a_texCoord顶点属性数据集
drawCell:addVertexAttribute("a_position", a_positionDataArray, 4, BKDataType.BKFloat3, false) -- 分别传入顶点属性名，数据集（只支持平铺的float数组），顶点个数，数据类型，是否需要自动归一化
drawCell:addVertexAttribute("a_color", a_colorDataArray, 4, BKDataType.BKFloat4, false) -- 分别传入顶点属性名，数据集（只支持平铺的float数组），顶点个数，数据类型，是否需要自动归一化
drawCell:addVertexAttribute("a_texCoord", a_texCoordDataArray, 4, BKDataType.BKFloat2, false) -- 分别传入顶点属性名，数据集（只支持平铺的float数组），顶点个数，数据类型，是否需要自动归一化
local mvpMatrixDataArray = {}
drawCell:addVertexUniform("mvpMatrix", mvpMatrixDataArray, 1, BKDataType.BKMat4) -- 分别传入Uniform名字，数据集，数组的个数（以数据类型为单位，不是Lua数组的float个数），数据类型
local alphaDataArray = { 1.0 }
drawCell:addFragmentUniform("alpha", alphaDataArray, 1, BKDataType.BKFloat) -- 添加片段着色器Uniform，注意即使数据只有一个float，也需要组织为lua数组传入
drawCell:addInputTextureWithName("inputImageTexture", gResourceRootPath.."/resources/image.png", true, false) -- 添加输入纹理，参数分别是纹理在着色器中的名字，图像路径，是否需同步加载图像, 是否为顶点着色器纹理
drawCell:addMainTextureAsInputTexture("inputImageTexture") -- 也可以将渲染执行时的主纹理作为输入纹理
drawCell:setDrawingMode(BKDrawingMode.BKDrawingModeTriangleStrip) -- 设置渲染模式为TriangleStrip，全部可选值有 BKDrawingMode.BKDrawingModeTriangleStrip/BKDrawingMode.BKDrawingModeTRIANGLES/BKDrawingMode.BKDrawingModeElements/BKDrawingMode.BKDrawingModeLINES/BKDrawingMode.BKDrawingModeElementsLINES/BKDrawingMode.BKDrawingModePoints
local drawingIndicesDataArray = {}
drawCell:setDrawingIndicesData(drawingIndicesDataArray, 6) -- 可以设置为BKDrawingModeElements渲染模式，但需要配合设置绘制索引
drawCell:setLoadAction(BKLoadAction.BKLoadActionClear) -- 设置DrawCall启动时清理渲染目标，还有其他可选值 BKLoadAction.BKLoadActionNothing(什么都不处理，性能最优，适合全图渲染的DrawCall)/BKLoadAction.BKLoadActionClear(以clearColor清空渲染目标) /BKLoadAction.BKLoadActionCopyInput(自动拷贝主纹理到当前渲染目标，只用于添加了主纹理作为输入纹理的情况) 
drawCell:setClearColor(1.0, 1.0, 1.0, 1.0) -- 设置启动时将渲染目标赋值为全白
drawCell:enableDefaultBlend() -- 开启标准混合模式， src = ONE, dst = SRC_ALPHA
drawCell:setDepthTestEnable(false) -- 可以按需开启深度测试，这里不需要所以传入false，默认就是关闭的
drawCell:setDepthCompareOp(BKDepthCompareOp.BKDepthCompareOpNever) -- 设置深度测试比较方法，全部可选值有BKDepthCompareOp.BKDepthCompareOpNone/BKDepthCompareOp.BKDepthCompareOpNever/BKDepthCompareOp.BKDepthCompareOpLess/BKDepthCompareOp.BKDepthCompareOpEqual/BKDepthCompareOp.BKDepthCompareOpLessOrEqual/BKDepthCompareOp.BKDepthCompareOpGreater/BKDepthCompareOp.BKDepthCompareOpNotEqual/BKDepthCompareOp.BKDepthCompareOpGreaterOrEqual/BKDepthCompareOp.BKDepthCompareOpAlways
drawCell:setDepthClearValue(1.0) -- 设置深度缓冲区默认值
gContextWrapper:addChild(drawCell) -- 添加到主渲染链根节点上

drawCell:updateUniformData("mvpMatrix", mvpMatrixDataArray) -- 
```

------

# 通用能力
## 类定义与继承
Beauytkit渲染引擎提供了默认类定义方案，关键方法如下
```lua
---创建类
---@param classParent Class 可选参数或其他类定义
function class(classParent) end
```

### 例子
```lua
MyNode = class(Node) -- 继承Node节点

--- 构造方法
function MyNode:ctor(nodeName)
    self:setName(nodeName) -- 可直接调用父类的方法
end

--- 覆写父类方法
function MyNode:getName()
    return 'Overrided getName: '...Node.getName(self) -- 显式调用父类被覆盖方法
end

local myNodeInstance = MyNode.new('MyNodeInstance')
print(myNodeInstance:getName()) -- 打印出'Overrided getName: MyNodeInstance'
```

------

## 延迟或定时调用
Beauytkit引擎内置延迟调用以及定时调用方法，同时提供方法绑定用于调用类方法
### 关键方法
```lua
---延迟调用
---@param callable 回调方法体，参数任意
---@param delay 延迟时间，以毫秒为单位
---@param ... 不定参数，回调执行时自动传入到方法参数列表中
---@return taskId，可用于后续取消任务调用
function postCallableWithDelay(callable, delay, ...) end

---延迟一帧调用
---@param callable 回调方法体，参数任意
---@param ... 不定参数，回调执行时自动传入到方法参数列表中
---@return taskId，可用于后续取消任务调用
function postCallable(callable, ...) end

---定时调用
---@param callable 回调方法体，参数任意
---@param interval 间隔执行时间，以毫秒为单位
---@param ... 不定参数，回调执行时自动传入到方法参数列表中
---@return taskId 可用于后续停止定时调用
function scheduleCallable(callable, interval, ...) end

---取消未被执行的延迟调用或正在运行的定时调用
---@param id postCallableWithDelay、postCallable、scheduleCallable的返回值
function stopCallable(id) end

---封装成员函数为一个普通闭包
---@param func 目标方法
---@param target 执行目标方法的对象
---@param ... 调用方法默认携带的参数列表
function BindFunction(func, target, ...) end
```

### 例子
```lua
-- case1：延迟1秒打印信息
postCallableWithDelay(function()
    print("called after 1 seconds")
end, 1000)

-- case2: 每隔一秒进行一次计数
scheduleCallable(function()
    GLOBAL_TICK = GLOBAL_TICK + 1
    print("current tick is", GLOBAL_TICK)
end, 1000)

-- case3: 延迟一帧调用类对象方法
local node = Node.new()
gContextWrapper:addChild(node)
postCallable(BindFunction(Node.setName, node), "MyNode")
```

------

## 全局常量
Beauytkit渲染引擎内建多个全局常量，引擎使用方不可修改其值，也无需require即可读取

### 全局根节点
只有直接或间接以gContextWrapper作为父节点的节点方可被处理渲染

### 画布宽度
通过gContextWidth全局常量获取引擎当前全局画布宽度，可被外部修改

### 画布高度
通过gContextHeight全局常量引擎当前全局画布高度，可被外部修改

### 当前帧到上一帧的间隔 
通过gTimeSinceLastFrame全局常量获取以毫秒为单位的帧间隔时间

### 引擎到目前为止的运行时间 
通过gElapsedTime全局常量获取以毫秒为单位的总运行时间

### 资源根目录 
通过gResourceRootPath全局常量获取引擎所运行的工作目录或理解为资源根目录。此路径不带/结尾，使用时注意拼接/符号

------

## 通用数据结构
Beautykit内置多个数据结构封装类，包括：Color3B, Color4B, Color4F, Vector2, Vector3, Vector4, Matrix, Rect, Size。调用接口时需要严格使用相应的对象。

* Color3B的使用

```lua
local color3b = Color3B.new(255, 255, 255) -- Color3B是以[0, 255]为取值范围表示rgb三个颜色通道
local rValue = color3b:r() -- 获取r颜色通道值
local gValue = color3b:g() -- 获取g颜色通道值
local bValue = color3b:b() -- 获取b颜色通道值
color3b:r(0) -- 独立设置r颜色通道值
color3b:g(0) -- 独立设置g颜色通道值
color3b:b(0) -- 独立设置b颜色通道值
color3b:set(0, 0, 0) -- 一次性设置rgb三个颜色通道值
```

* Color4B的使用

```lua
local color4b = Color4B.new(255, 255, 255, 255) -- Color4B是以[0, 255]为取值范围表示rgba四个颜色通道
local rValue = color4b:r() -- 获取r颜色通道值
local gValue = color4b:g() -- 获取g颜色通道值
local bValue = color4b:b() -- 获取b颜色通道值
local aValue = color4b:a() -- 获取a颜色通道值
color4b:r(0) -- 独立设置r颜色通道值
color4b:g(0) -- 独立设置g颜色通道值
color4b:b(0) -- 独立设置b颜色通道值
color4b:a(0) -- 独立设置a颜色通道值
color4b:set(0, 0, 0, 0) -- 一次性设置rgba四个颜色通道值
```

* Color4F的使用

```lua
local color4f = Color4B.new(1.0, 1.0, 1.0, 1.0) -- Color4F是以[0.0, 1.0]为取值范围表示rgba四个颜色通道
local rValue = color4f:r() -- 获取r颜色通道值
local gValue = color4f:g() -- 获取g颜色通道值
local bValue = color4f:b() -- 获取b颜色通道值
local aValue = color4f:a() -- 获取a颜色通道值
color4f:r(0.0) -- 独立设置r颜色通道值
color4f:g(0.0) -- 独立设置g颜色通道值
color4f:b(0.0) -- 独立设置b颜色通道值
color4f:a(0.0) -- 独立设置a颜色通道值
color4f:set(0.0, 0.0, 0.0, 0.0) -- 一次性设置rgba四个颜色通道值
```

* Vector2的使用

```lua
local vec2 = Vector2.new(100.0, 100.0) -- Vector2由两个float组成，创建时可以忽略初始值
local xValue = vec2:x() -- 获取x值
local yValue = vec2:y() -- 获取y值
vec2:x(0.0) -- 设置x值
vec2:y(0.0) -- 设置y值
local isAllZero = vec2:isZero() -- 判断Vector2当前两个分量值是否均为0.0
local isAllOne = vec2:isOne() -- 判断Vector2当前两个分量值是否均为1.0
local anotherVec2 = Vector2.new(vec2) -- 可拷贝传入的Vector2对象值创建新的Vector2对象
vec2:add(anotherVec2) -- 执行矢量加法，结果直接修改到方法调用方，即此例中的vec2对象
vec2:subtract(anotherVec2) -- 执行矢量减法，结果直接修改到方法调用方，即此例中的vec2对象
vec2:clamp(Vector2.new(0.0, 0.0), Vector2.new(100.0, 100.0)) -- 对vec2对象中的xy分量分别限制在[0.0, 100.0]之间
vec2:set(0.0, 0.0) -- 一次性设置Vector2两个分量值
vec2:set(anotherVec2) -- 拷贝传入对象的分量值
local distance = vec2:distance(anotherVec2) -- 计算两个矢量间的距离
local distanceSquared = vec2:distanceSquared(anotherVec2) -- 计算两个矢量间的距离平方值
local dot = vec2:dot(anotherVec2) -- 执行矢量点乘计算
local length = vec2:length() -- 计算当前矢量的长度
local lengthSquared = vec2:lengthSquared() -- 计算当前矢量的长度平方值
vec2:negate() -- 对当前矢量分量进行负运算
vec2:normalize() -- 对当前矢量进行归一化运算
vec2:scale(2.0) -- 对当前矢量各分量缩放指定倍数
vec2:scale(anotherVec2) -- 对当前矢量各分量分别缩放传入Vector2各分量值倍数
vec2:rotate(Vector2.new(1.0, 0.0), 3.14 / 2.0) -- 使当前矢量绕指定矢量旋转一定的角度，角度以弧度制表示
vec2:reflect(Vector2.new(1.0, 1.0)) -- 对当前矢量进行反射运算，结果直接修改到方法调用方，即此例中的vec2对象
local angleBetweenTwoVector = Vector2.angle(vec2, anotherVec2) -- 计算两个矢量之前的夹角（以弧度制为单位）
```

* Vector3的使用

```lua
local vec3 = Vector3.new(100.0, 100.0, 100.0) -- Vector3由三个float组成，创建时可以忽略初始值
local xValue = vec3:x() -- 获取x值
local yValue = vec3:y() -- 获取y值
local zValue = vec3:z() -- 获取z值
vec3:x(0.0) -- 设置x值
vec3:y(0.0) -- 设置y值
vec3:z(0.0) -- 设置z值
local isAllZero = vec3:isZero() -- 判断Vector3当前三个分量值是否均为0.0
local isAllOne = vec3:isOne() -- 判断Vector3当前三个分量值是否均为1.0
local anotherVec3 = Vector3.new(vec3) -- 可拷贝传入的Vector3对象值创建新的Vector3对象
vec3:add(anotherVec3) -- 执行矢量加法，结果直接修改到方法调用方，即此例中的vec3对象
vec3:subtract(anotherVec3) -- 执行矢量减法，结果直接修改到方法调用方，即此例中的vec3对象
vec3:clamp(Vector3.new(0.0, 0.0, 0.0), Vector3.new(100.0, 100.0, 100.0)) -- 对vec3对象中的xyz分量分别限制在[0.0, 100.0]之间
vec3:set(0.0, 0.0, 0.0) -- 一次性设置Vector3三个分量值
vec3:set(anotherVec3) -- 拷贝传入对象的分量值
local distance = vec3:distance(anotherVec3) -- 计算两个矢量间的距离
local distanceSquared = vec3:distanceSquared(anotherVec3) -- 计算两个矢量间的距离平方值
local dot = vec3:dot(anotherVec3) -- 执行矢量点乘计算
local cross = vec3:cross(anotherVec3) -- 执行矢量叉乘计算
local length = vec3:length() -- 计算当前矢量的长度
local lengthSquared = vec3:lengthSquared() -- 计算当前矢量的长度平方值
vec3:negate() -- 对当前矢量分量进行负运算
vec3:normalize() -- 对当前矢量进行归一化运算
vec3:scale(2.0) -- 对当前矢量各分量缩放指定倍数
vec3:scale(anotherVec3) -- 对当前矢量各分量分别缩放传入Vector3各分量值倍数
vec3:rotate(Vector3.new(1.0, 0.0, 0.0), 3.14 / 2.0) -- 使当前矢量绕指定矢量旋转一定的角度，角度以弧度制表示
vec3:reflect(Vector3.new(1.0, 1.0, 1.0)) -- 对当前矢量进行反射运算，结果直接修改到方法调用方，即此例中的vec3对象
local angleBetweenTwoVector = Vector3.angle(vec3, anotherVec3) -- 计算两个矢量之前的夹角（以弧度制为单位）
```

* Vector4的使用

```lua
local vec4 = Vector4.new(100.0, 100.0, 100.0, 100.0) -- Vector4由四个float组成，创建时可以忽略初始值
local xValue = vec4:x() -- 获取x值
local yValue = vec4:y() -- 获取y值
local zValue = vec4:z() -- 获取z值
local wValue = vec4:w() -- 获取w值
vec4:x(0.0) -- 设置x值
vec4:y(0.0) -- 设置y值
vec4:z(0.0) -- 设置z值
vec4:w(0.0) -- 设置2值
local isAllZero = vec4:isZero() -- 判断Vector4当前四个分量值是否均为0.0
local isAllOne = vec4:isOne() -- 判断Vector4当前四个分量值是否均为1.0
local anotherVec4 = Vector4.new(vec4) -- 可拷贝传入的Vector4对象值创建新的Vector4对象
vec4:add(anotherVec4) -- 执行矢量加法，结果直接修改到方法调用方，即此例中的vec4对象
vec4:subtract(anotherVec4) -- 执行矢量减法，结果直接修改到方法调用方，即此例中的vec4对象
vec4:clamp(Vector4.new(0.0, 0.0, 0.0, 0.0), Vector4.new(100.0, 100.0, 100.0, 100.0)) -- 对vec4对象中的xyzw分量分别限制在[0.0, 100.0]之间
vec4:set(0.0, 0.0, 0.0, 0.0) -- 一次性设置Vector4四个分量值
vec4:set(anotherVec4) -- 拷贝传入对象的分量值
local distance = vec4:distance(anotherVec4) -- 计算两个矢量间的距离
local distanceSquared = vec4:distanceSquared(anotherVec4) -- 计算两个矢量间的距离平方值
local dot = vec4:dot(anotherVec4) -- 执行矢量点乘计算
local length = vec4:length() -- 计算当前矢量的长度
local lengthSquared = vec4:lengthSquared() -- 计算当前矢量的长度平方值
vec4:negate() -- 对当前矢量分量进行负运算
vec4:normalize() -- 对当前矢量进行归一化运算
vec4:scale(2.0) -- 对当前矢量各分量缩放指定倍数
vec4:scale(anotherVec4) -- 对当前矢量各分量分别缩放传入Vector4各分量值倍数
local angleBetweenTwoVector = Vector4.angle(vec4, anotherVec4) -- 计算两个矢量之前的夹角（以弧度制为单位）
```

* Matrix的使用

```lua
local matrix = Matrix.new() -- 创建一个4x4的单位矩阵
local isIdentityMatrix = matrix:isIdentity() -- 判断矩阵对象是否为单位矩阵

local eyePosition = Vector3.new(100.0, 100.0, 100.0) -- 观察者位置
local targetPosition = Vector3.new(0.0, 0.0, 0.0) -- 被观察对象位置
local upVec = Vector3.new(0.0, 1.0, 0.0) -- 观察者上向量
Matrix.createLookAt(eyePosition, targetPosition, up, matrix) -- 创建相机矩阵便捷方法，结果储存到最后一个Matrix参数中

local fieldOfView = 30.0 -- y 方向的视野(以角度为单位).
local aspectRatio = 1920.0 / 1080.0 -- 纵横比
local zNearPlane = 1.0 -- 近平面距离
local zFarPlane = 99.0 -- 远平面距离
Matrix.createPerspective(fieldOfView, aspectRatio, zNearPlane, zFarPlane, matrix) -- 创建透视投影矩阵，结果储存在最后一个Matrix参数中

local width = 1920.0 -- 视图的宽度
local height = 1080.0 -- 视图的高度
local zNearPlane = 0.0 -- 近平面深度
local zFarPlane = 99.0 -- 远平面深度
Matrix.createOrthographic(width, height, zNearPlane, zFarPlane, matrix) -- 创建正交投影矩阵，结果储存在最后一个Matrix参数中

local objectPosition = Vector3.new(0.0, 0.0, 0.0) -- 目标物体位置
local cameraPosition = Vector3.new(100.0, 100.0, 0.0) -- 相机位置
local cameraUpVector = Vector3.new(0.0, 1.0, 0.0) -- 相机上向量
Matrix.createBillboard(objectPosition, cameraPosition, cameraUpVector, matrix) -- 创建广告牌相机矩阵，结果储存在最后一个Matrix参数中

local scaleValue = Vector3.new(2.0, 2.0, 2.0) -- 定义缩放值
Matrix.createScale(scaleValue, matrix) -- 创建缩放矩阵
Matrix.createScale(scaleValue:x(), scaleValue:y(), scaleValue:z(), matrix) -- 创建缩放矩阵的第二种方式，结果储存在最后一个Matrix参数中

local axis = Vector3.new(0.0, 1.0, 0.0) -- 旋转轴
local angle = 3.14 / 2.0 -- 旋转角度 (弧度制)
Matrix.createRotation(axis, angle, matrix) -- 创建旋转矩阵

local yaw = 3.14 / 2.0 -- yaw角度 (弧度制)
local pitch = 0.0 -- pitch角度 (弧度制)
local roll = 0.0 -- roll角度 (弧度制)
Matrix.createFromEuler(yaw, pitch, roll, dst) -- 基于欧拉角的方式创建旋转矩阵，结果储存在最后一个Matrix参数中

local translationValue = Vector3.new(100.0, 100.0, 100.0)
Matrix.createTranslation(translationValue, matrix) -- 创建位移矩阵，结果储存在最后一个Matrix参数中
Matrix.createTranslation(translationValue:x(), translationValue:y(), translationValue:z(), matrix) -- 创建位移矩阵的第二种方式，结果储存在最后一个Matrix参数中

local anotherMatrix = Matrix.new(Matrix.zero()) -- 创建一个4x4且分量全为0.0的矩阵
matrix:add(1.0) -- 矩阵所有分量加上传入的标量值
matrix:add(anotherMatrix) -- 执行矩阵加法
matrix:subtract(anotherMatrix) -- 执行矩阵减法
matrix:multiply(2.0) -- 矩阵所有分量乘上传入的标量值
matrix:multiple(anotherMatrix) -- 执行矩阵乘法
local determinant = matrix:determinant() -- 计算矩阵的行列式
matrix:invert() -- 对矩阵进行取逆操作
matrix:transpose() -- 对矩阵进行转置操作
matrix:negate() -- 对矩阵进行负运算
matrix:setIdentity() -- 将矩阵重置为单位矩阵
matrix:setZero() -- 将矩阵所有分量设置为0.0
local dataArray = matrix:mDataArray() -- 获取矩阵的16个分量，返回值为float数组
dataArray[1] = 1.0 -- 可直接修改数组
matrix:set(dataArray) -- 将新的数据设置回去矩阵中

local point3 = Vector3.new(100.0, 100.0, 100.0)
matrix:transformVector(point) -- 对Vector3进行矩阵变换
local point4 = Vector4.new(100.0, 100.0, 100.0, 0.0)
matrix:transformVector(point) -- 也支持对Vector4进行矩阵变换
```

* Rect的使用

```lua
local rect = Rect.new(0.0, 1.0, 100.0, 200.0) -- 创建一个x = 0.0, y = 1.0, width = 100.0, height = 200.0的Rect对象，可以忽略参数创建一个值全为0.0的Rect对象
local anotherRect = Rect.new(rect) -- 也可以拷贝另外一个Rect对象进行创建
local xValue = rect:x() -- 获取rect对象的x值
local yValue = rect:y() -- 获取rect对象的y值
local widthValue = rect:width() -- 获取rect对象的width值
local heightValue = rect:height() -- 获取rect对象的height值
rect:x(100.0) -- 设置rect对象的x值
rect:y(200.0) -- 设置rect对象的y值
rect:width(1000.0) -- 设置rect对象的width值
rect:height(1000.0) -- 设置rect对象的height值
local isEqual = rect:equals(anotherRect) -- 判断两个rect对象是否相等
local isIntersects = rect:intersects(anotherRect) -- 判断两个矩形是否存在相交区域
```

* Size的使用

```lua
local size = Size.new(100.0, 100.0) -- 创建一个width = 100.0, height = 100.0的Size对象，可以忽略参数创建一个值全为0.0的Size对象
local anotherSize = Size.new(size) -- 也可以拷贝另外一个Size对象进行创建
local widthValue = size:width() -- 获取size对象的width值
local heightValue = size:height() -- 虎丘size对象的height值
size:width(3000.0) -- 设置size对象的width值
size:height(3000.0) -- 设置size对象的height值
local isEqual = size:equals(anotherSize) -- 判断两个size对象是否相等
```
<API>
```lua
---@class Matrix
Matrix = {}

---创建单位矩阵
---@return Matrix 单位矩阵
function Matrix.identity() end

---创建全为零的矩阵
---@return Matrix 全0矩阵
function Matrix.zero() end

---根据指定的输入参数创建视图矩阵
---@param eyePosition Vector3 { comment = "眼睛位置 " }
---@param targetPosition Vector3 { comment = "目标中心点位置 " }
---@param up Vector3 { comment = "上向量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createLookAt(eyePosition, targetPosition, up, dst) end

---根据指定的输入参数创建视图矩阵
---@param eyePositionX number { comment = "眼睛位置x坐标 " }
---@param eyePositionY number { comment = "眼睛位置y坐标 " }
---@param eyePositionZ number { comment = "眼睛位置z坐标 " }
---@param targetCenterX number { comment = "目标中心点位置x坐标 " }
---@param targetCenterY number { comment = "目标中心点位置y坐标 " }
---@param targetCenterZ number { comment = "目标中心点位置z坐标 " }
---@param upX number { comment = "上向量x坐标 " }
---@param upY number { comment = "上向量y坐标 " }
---@param upZ number { comment = "上向量z坐标 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createLookAt(eyePositionX, eyePositionY, eyePositionZ, targetCenterX, targetCenterY, targetCenterZ, upX, upY, upZ, dst) end

---根据输入参数构建透视投影矩阵
---@param fieldOfView number { comment = "y 方向的视野 (角度值)." }
---@param aspectRatio number { comment = "纵横比 " }
---@param zNearPlane number { comment = "近平面距离 " }
---@param zFarPlane number { comment = "远平面距离 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createPerspective(fieldOfView, aspectRatio, zNearPlane, zFarPlane, dst) end

---创建正交投影矩阵
---@param width number { comment = "视图的宽度 " }
---@param height number { comment = "视图的高度 " }
---@param zNearPlane number { comment = "视图区域最小深度 " }
---@param zFarPlane number { comment = "视图区域最大深度 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createOrthographic(width, height, zNearPlane, zFarPlane, dst) end

---创建正交投影矩阵
---@param left number { comment = "视图区域的最小横坐标 " }
---@param right number { comment = "视图区域的最大横坐标 " }
---@param bottom number { comment = "视图区域的最小纵坐标 " }
---@param top number { comment = "视图区域的最大纵坐标 " }
---@param zNearPlane number { comment = "视图区域最小深度 " }
---@param zFarPlane number { comment = "视图区域最大深度 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createOrthographicOffCenter(left, right, bottom, top, zNearPlane, zFarPlane, dst) end

---创建一个围绕指定对象位置旋转的球形广告牌
---@param objectPosition Vector3 { comment = "目标物体位置 " }
---@param cameraPosition Vector3 { comment = "相机位置 " }
---@param cameraUpVector Vector3 { comment = "相机上向量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createBillboard(objectPosition, cameraPosition, cameraUpVector, dst) end

---创建一个围绕指定对象位置旋转的球形广告牌
---@param objectPosition Vector3 { comment = "目标物体位置 " }
---@param cameraPosition Vector3 { comment = "相机位置 " }
---@param cameraUpVector Vector3 { comment = "相机上向量 " }
---@param cameraForwardVector Vector3 { comment = "相机前向量如果目标位置太近，使用此参数 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createBillboard(objectPosition, cameraPosition, cameraUpVector, cameraForwardVector, dst) end

---创建缩放矩阵
---@param scale Vector3 { comment = "缩放值 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createScale(scale, dst) end

---创建缩放矩阵
---@param xScale number { comment = "x分量上的缩放值 " }
---@param yScale number { comment = "y分量上的缩放值 " }
---@param zScale number { comment = "z分量上的缩放值 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createScale(xScale, yScale, zScale, dst) end

---通过给定的四元数创建旋转矩阵
---@param quat Quaternion { comment = "描述三维旋转的四元数 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createRotation(quat, dst) end

---通过给定的旋转轴和角度（弧度制）创建矩阵
---@param axis Vector3 { comment = "旋转轴 " }
---@param angle number { comment = "旋转角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createRotation(axis, angle, dst) end

---创建绕X坐标轴渲染的旋转矩阵
---@param angle number { comment = "旋转角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createRotationX(angle, dst) end

---创建绕Y坐标轴渲染的旋转矩阵
---@param angle number { comment = "旋转角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createRotationY(angle, dst) end

---创建绕Z坐标轴渲染的旋转矩阵
---@param angle number { comment = "旋转角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createRotationZ(angle, dst) end

---通过欧拉角创建旋转矩阵
---@param yaw number { comment = "yaw角度 (弧度制)" }
---@param pitch number { comment = "pitch角度 (弧度制)" }
---@param roll number { comment = "roll角度 (弧度制)" }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createFromEuler(yaw, pitch, roll, dst) end

---创建位移矩阵
---@param translation Vector3 { comment = "偏移量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createTranslation(translation, dst) end

---创建位移矩阵
---@param xTranslation number { comment = "X方向上的偏移量 " }
---@param yTranslation number { comment = "Y方向上的偏移量 " }
---@param zTranslation number { comment = "Z方向上的偏移量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.createTranslation(xTranslation, yTranslation, zTranslation, dst) end

---向该矩阵的每个分量添加一个标量值
---@param scalar number { comment = "要添加的标量 " }
function Matrix:add(scalar) end

---向该矩阵的每个分量添加一个标量值并将结果存储到dst矩阵中
---@param scalar number { comment = "要添加的标量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:add(scalar, dst) end

---将指定的矩阵与传入矩阵相加并将结果存储到当前矩阵
---@param m Matrix { comment = "要相加的矩阵 " }
function Matrix:add(m) end

---将指定的矩阵相加并将结果存储在dst矩阵中
---@param m1 Matrix { comment = "第一个矩阵 " }
---@param m2 Matrix { comment = "第二个矩阵 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.add(m1, m2, dst) end

---分解此矩阵的缩放、旋转和平移分量。
---@param scale Vector3 { comment = "缩放分量. " }
---@param rotation Quaternion { comment = "渲染分量. " }
---@param translation Vector3 { comment = "平移分量. " }
---@return boolean 
function Matrix:decompose(scale, rotation, translation) end

---计算此矩阵的行列式
---@return number 行列式
function Matrix:determinant() end

---获取此矩阵的缩放分量到指定三元矢量中
---@param scale Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getScale(scale) end

---获取此矩阵的旋转分量到指定的四元数中
---@param rotation Quaternion { comment = "用于存储结果的四元数 " }
---@return boolean 如果成功提取则返回true，否则返回false
function Matrix:getRotation(rotation) end

---获取此矩阵的平移分量到指定的三元矢量中
---@param translation Vector3 { comment = "用于存结果的三元矢量 " }
function Matrix:getTranslation(translation) end

---获取此矩阵的向上向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getUpVector(dst) end

---获取此矩阵的向下向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getDownVector(dst) end

---获取此矩阵的向左向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getLeftVector(dst) end

---获取此矩阵的向右向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getRightVector(dst) end

---获取此矩阵的向前向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getForwardVector(dst) end

---获取此矩阵的向后向量
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:getBackVector(dst) end

---对此矩阵进行求逆
---@return boolean 成功求逆则返回true，否则返回false
function Matrix:invert() end

---对此矩阵进行求逆并将结果存储在dst中
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
---@return boolean 成功求逆则返回true，否则返回false
function Matrix:invert(dst) end

---确定当前矩阵是否为单位矩阵
---@return boolean 如果当前矩阵为单位矩阵则返回true，否则返回false
function Matrix:isIdentity() end

---将当前矩阵的分量乘以一个标量
---@param scalar number { comment = "The scalar value." }
function Matrix:multiply(scalar) end

---将当前矩阵的分量乘以一个标量并将结果存储在 dst 中
---@param scalar number { comment = "The scalar value." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:multiply(scalar, dst) end

---将指定矩阵的分量乘以一个标量并将结果存储在 dst 中
---@param m Matrix { comment = "矩阵. " }
---@param scalar number { comment = "乘数标量. " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.multiply(m, scalar, dst) end

---将当前矩阵与输入矩阵进行相乘
---@param m Matrix { comment = "待相乘矩阵 " }
function Matrix:multiply(m) end

---将输入的矩阵进行想乘并将结果存储在dst中
---@param m1 Matrix { comment = "第一个矩阵 " }
---@param m2 Matrix { comment = "第二个矩阵 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.multiply(m1, m2, dst) end

---将当前矩阵进行负运算，并将结果存储在当前矩阵中
function Matrix:negate() end

---将当前矩阵进行负运算，并将结果存储在dst中
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:negate(dst) end

---将此矩阵与指定四元数旋转对应的矩阵后乘，并将结果存储在当前矩阵中
---@param q Quaternion { comment = "用于旋转的四元数 " }
function Matrix:rotate(q) end

---将此矩阵与指定四元数旋转对应的矩阵后乘，并将结果存储在 dst 中
---@param q Quaternion { comment = "用于旋转的四元数 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:rotate(q, dst) end

---将此矩阵与绕指定轴的指定旋转对应的矩阵后乘，并将结果存储在当前矩阵中
---@param axis Vector3 { comment = "旋转轴 " }
---@param angle number { comment = "角度 (弧度制)." }
function Matrix:rotate(axis, angle) end

---将此矩阵与绕指定轴的指定旋转对应的矩阵后乘，并将结果存储在 dst 中
---@param axis Vector3 { comment = "旋转轴 " }
---@param angle number { comment = "角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:rotate(axis, angle, dst) end

---将此矩阵与沿X轴旋转指定角度对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param angle number { comment = "角度 (弧度制)." }
function Matrix:rotateX(angle) end

---将此矩阵与沿X轴旋转指定角度对应的矩阵进行后乘，并将结果存储在 dst 中
---@param angle number { comment = "角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:rotateX(angle, dst) end

---将此矩阵与沿Y轴旋转指定角度对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param angle number { comment = "角度 (弧度制)." }
function Matrix:rotateY(angle) end

---将此矩阵与沿Y轴旋转指定角度对应的矩阵进行后乘，并将结果存储在 dst 中
---@param angle number { comment = "角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:rotateY(angle, dst) end

---将此矩阵与沿Z轴旋转指定角度对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param angle number { comment = "角度 (弧度制)." }
function Matrix:rotateZ(angle) end

---将此矩阵与沿Z轴旋转指定角度对应的矩阵进行后乘，并将结果存储在 dst 中
---@param angle number { comment = "角度 (弧度制)." }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:rotateZ(angle, dst) end

---将此矩阵与指定尺度变换对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param value number { comment = "所有轴的缩放量 " }
function Matrix:scale(value) end

---将此矩阵与指定尺度变换对应的矩阵进行后乘，并将结果存储在 dst 中
---@param value number { comment = "所有轴的缩放量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:scale(value, dst) end

---将此矩阵与指定尺度变换对应的矩阵后乘，并将结果存储在当前矩阵中
---@param xScale number { comment = "x轴上的缩放量 " }
---@param yScale number { comment = "y轴上的缩放量 " }
---@param zScale number { comment = "z轴上的缩放量 " }
function Matrix:scale(xScale, yScale, zScale) end

---将此矩阵与指定尺度变换对应的矩阵后乘，并将结果存储在 dst 中
---@param xScale number { comment = "x轴上的缩放量 " }
---@param yScale number { comment = "y轴上的缩放量 " }
---@param zScale number { comment = "z轴上的缩放量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:scale(xScale, yScale, zScale, dst) end

---将此矩阵与指定尺度变换对应的矩阵后乘，并将结果存储在当前矩阵中
---@param s Vector3 { comment = "沿 x、y 和 z 轴的缩放值" }
function Matrix:scale(s) end

---将此矩阵与指定尺度变换对应的矩阵后乘，并将结果存储在 dst 中
---@param s Vector3 { comment = "沿 x、y 和 z 轴的缩放值" }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:scale(s, dst) end

---设置此矩阵的值.
---@param m11 number { comment = "第一行的第一个元素值 " }
---@param m12 number { comment = "第一行的第二个元素值 " }
---@param m13 number { comment = "第一行的第三个元素值 " }
---@param m14 number { comment = "第一行的第四个元素值 " }
---@param m21 number { comment = "第二行的第一个元素值 " }
---@param m22 number { comment = "第二行的第二个元素值 " }
---@param m23 number { comment = "第二行的第三个元素值 " }
---@param m24 number { comment = "第二行的第四个元素值 " }
---@param m31 number { comment = "第三行的第一个元素值 " }
---@param m32 number { comment = "第三行的第二个元素值 " }
---@param m33 number { comment = "第三行的第三个元素值 " }
---@param m34 number { comment = "第三行的第四个元素值 " }
---@param m41 number { comment = "第四行的第一个元素值 " }
---@param m42 number { comment = "第四行的第二个元素值 " }
---@param m43 number { comment = "第四行的第三个元素值 " }
---@param m44 number { comment = "第四行的第四个元素值 " }
function Matrix:set(m11, m12, m13, m14, m21, m22, m23, m24, m31, m32, m33, m34, m41, m42, m43, m44) end

---将此矩阵的值设置为指定列主数组中的值
---@param m number { comment = "包含 16 个以列为主格式的元素的数组" }
function Matrix:set(m) end

---将此矩阵的值设置为指定矩阵的值
---@param m Matrix { comment = "矩阵输入源 " }
function Matrix:set(m) end

---将当前矩阵设置为单位矩阵
function Matrix:setIdentity() end

---将当前矩阵所有元素设置为0
function Matrix:setZero() end

---将当前矩阵减去输入矩阵
---@param m Matrix { comment = "The matrix to subtract." }
function Matrix:subtract(m) end

---将输入的矩阵进行相减并将结果存储在dst中
---@param m1 Matrix { comment = "第一个矩阵 " }
---@param m2 Matrix { comment = "第二个矩阵 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix.subtract(m1, m2, dst) end

---使用当前矩阵对指定的矢量进行变换（将输入的矢量的w分量视为0）
---@param vector Vector3 { comment = "需要变换的矢量 " }
function Matrix:transformVector(vector) end

---使用当前矩阵对指定的矢量进行变换（将输入的矢量的w分量视为0）
---@param vector Vector3 { comment = "需要变换的矢量 " }
---@param dst Vector3 { comment = "用于存储变换结果的矢量 " }
function Matrix:transformVector(vector, dst) end

---使用当前矩阵对指定的矢量进行变换
---@param x number { comment = "矢量x值 " }
---@param y number { comment = "矢量y值 " }
---@param z number { comment = "矢量z值 " }
---@param w number { comment = "矢量w值 " }
---@param dst Vector3 { comment = "用于存储结果的三元矢量 " }
function Matrix:transformVector(x, y, z, w, dst) end

---使用当前矩阵对指定的矢量进行变换
---@param vector Vector4 { comment = "需要变换的矢量 " }
function Matrix:transformVector(vector) end

---使用当前矩阵对指定的矢量进行变换
---@param vector Vector4 { comment = "需要变换的矢量 " }
---@param dst Vector4 { comment = "用于存储变换结果的矢量 " }
function Matrix:transformVector(vector, dst) end

---将此矩阵与指定平移对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param x number { comment = "x轴上的平移量 " }
---@param y number { comment = "y轴上的平移量 " }
---@param z number { comment = "z轴上的平移量 " }
function Matrix:translate(x, y, z) end

---将此矩阵与指定平移对应的矩阵进行后乘，并将结果存储在dst矩阵中
---@param x number { comment = "x轴上的平移量 " }
---@param y number { comment = "y轴上的平移量 " }
---@param z number { comment = "z轴上的平移量 " }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:translate(x, y, z, dst) end

---将此矩阵与指定平移对应的矩阵进行后乘，并将结果存储在当前矩阵中
---@param t Vector3 { comment = "沿x, y, z方向的平移量" }
function Matrix:translate(t) end

---将此矩阵与指定平移对应的矩阵进行后乘，并将结果存储在 dst 中
---@param t Vector3 { comment = "沿x, y, z方向的平移量" }
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:translate(t, dst) end

---对此本矩阵进行转置
function Matrix:transpose() end

---将当前矩阵进行转置并将结果存储在dst矩阵中
---@param dst Matrix { comment = "用于存储结果的矩阵 " }
function Matrix:transpose(dst) end

---获取矩阵原始数据指针对象
---@return lightuserdata { comment = "c++原始数据指针" }
function Matrix:m() end

---获取矩阵16个数据元素
---@return table { comment = "矩阵16个数据元素" }
function Matrix:mDataArray() end
```
</API>
------

# 读取JSON
```lua
local xxx_json = rapidjson.load(gResourceRootPath .. "xxx.json") --
xxx_json.frame -- 读取frame字段
```
## 获取AI识别数据

# 常见问题

------

* 为什么我的通用Shader总是编译不通过（Beautykit通用着色器规范）
  
  首先Beautykit的通用着色器有明确的后缀名，顶点着色器文件后缀为bkvert，片段着色器文件后缀为bkfrag。
  然后Beautykit的通用着色器是基于GLES3.1的语法编写，为了更好的兼容性，Beautykit对着色器语法有如下额外的规范
  1. 着色器的输入必须写layout(location=x) 其中x为属性的索引，例如layout(location=0) in vec3 a_position;
  2. 着色器的输出必须写layout(location=x) 其中x为属性的索引，例如layout(location=0) out vec2 textureCoordinate;
  3. 着色器的Uniform必须以Uniform Block的形式编写，且必须带layout(binding=x) 其中x为块的索引，如果着色器有多个uniform块则索引从0递增
  4. 顶点着色器以及片段着色器的Uniform Block名字不可以相同
  5. 着色器中的方法不允许直接使用Uniform，必须通过参数传入
  6. 不要在Uniform中使用vec3或int3
  7. 无论顶点着色器还是片段着色器，首行必须写#version 310 es

------

* 为什么在各种节点的回调函数里析构节点会出现崩溃
  
  因为所以的回调功能都是线程安全的，触发回调都是由节点自身触发，但在回调中析构自身相当于后续必然访问野指针，崩溃是必然的。因此如果有相关的需求，可以使用deactivate以及delayDestroy组合实现平替，例如以下代码
```lua
    node:scheduleUpdate(function(nodeSelf)
        if gSomethingCondition == true then
            nodeSelf:deactivate() -- 停止目标节点在当前帧的渲染
            nodeSelf:delayDestroy() -- 延迟一帧析构，保证安全
        end
    end)
```

------

* 为什么使用UILabel或者UIImage获取contentSize始终为0
  
  在排除不是不是内容自身缺失的情况下，检查节点是否在Canvas的节点树下，UILabel或UIImage均依赖Canvas获取真实资源以执行contentSize的计算，即不在Canvas的节点树下，contentSize无法自动计算出正确的值。

------

* 为什么UILabel重新设置文本后，contentSize没有更新

  首先确定UILabel是否在Canvas的节点树下，然后确定设置曾经设置过LabelSize（设置了之后永远都以这个值计算宽高），或者在UILinearLayout下使用了LayoutWeight（这个自动设置LabelSize），如果在强要求UILabel完全跟随文本的大小，在不确定下可以考虑优先调用一次setLabelSize(0.0, 0.0)。

------

* 为什么UILabel调用setMaxLineWidth且文本长度明显超过设定值也没有换行
  
  UILabel的换行逻辑优先使用LabelSize的宽度值（默认值为0），maxLineWidth的作用仅仅是需要在LabelSize范围内设定一个较小的值实现提前换行，实现一种类似padding的效果。

------

* 为什么在postCallableWithDelay回调中调用postCallable，新增的callable会在同一帧被调用，不符合预期在下一帧调用
  
  是因为postCallable存在设计缺陷，可以采用如下调用方式规避bug
  ```lua
  -- 使用固定延时调用来替换postCallable
  postCallableWithDelay(function() end, gTimeSinceLastFrame + 1.0)
  ```

------
  
* 为什么调用UIImage的getOriginImageSize方法获取到的大小为0
  
  首先考虑是不是路径传入错误，如果路径是正确的，确认调用getOriginImageSize时UIImage是否在canvas树中，如果不在canvas树中，uiimage是没法读取到图像的，因此会返回大小为0

------

* 为什么UIShape的drawCircle或者drawRect不能调整线宽
  
  UIShape默认是不支持线宽的，但我们可以通过drawSolidXXXWithBorder系列方法变相实现带宽度框线图形。
  ```lua
    local canvas = Canvas.create()
    gContextWrapper:addChild(canvas)

    local staticShape = UIShape.create()
    staticShape:drawSolidRectWithBorder(Vector2.new(200.0, 200.0), Vector2.new(400.0, 400.0), Color4F.new(0.0, 0.0, 0.0, 0.0), 5.0, Color4F.new(1.0, 0.0, 1.0, 1.0)) -- 透明填充色+有色边界 变相实现中空有带宽度的边框
  ```

  ------
