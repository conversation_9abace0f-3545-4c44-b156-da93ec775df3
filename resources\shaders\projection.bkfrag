#version 310 es

precision mediump float;

layout(location = 0) in vec4 texCoord; // 背景纹理坐标（屏幕空间）
layout(location = 1) in vec2 maskTexCoord; // 掩模纹理坐标（前景面片原始坐标）
layout(location = 2) in float intensity;
layout(location = 3) in float originalDepth;
layout(location = 4) in float projectedDepth;

layout(binding = 0) uniform sampler2D BG_TEX;
layout(binding = 1) uniform sampler2D MASK_TEX;

layout(binding = 0) uniform FragUniforms {
  vec3 projectionColor;  // 外部指定的投影颜色
  float depthCompare;    // 深度比较方向
  float depthRedundancy; // 深度比较冗余
};

layout(location = 0) out vec4 gl_FragColor;

void main() {

  float depthDiff = step(depthRedundancy,(originalDepth - projectedDepth) * depthCompare );

  vec4 base = texture(BG_TEX, (texCoord.xy / texCoord.w) * 0.5 + 0.5);
  vec4 overlayer = texture(MASK_TEX, maskTexCoord);
  overlayer = vec4(projectionColor * overlayer.r, overlayer.a);
  gl_FragColor = overlayer * base + overlayer * (1.0 - base.a) +
                 base * (1.0 - overlayer.a);
  gl_FragColor = mix(base, gl_FragColor, intensity * depthDiff);
}
