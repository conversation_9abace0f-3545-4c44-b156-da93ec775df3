#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3DS Max数据提取和转换合并脚本
合并extract_3ds_data.py和convert_3ds_relative.py的功能
从3ds.ms文件中提取相机和点数据，同时读取video_data.json，
将所有数据转换为相对坐标系并合并输出到一个统一的JSON文件中
"""

import re
import json
import os
import math
import numpy as np
from typing import Dict, List, Tuple, Any
from pathlib import Path


def extract_camera_data(file_path: Path) -> Dict[str, Any]:
    """
    从3ds.ms文件中提取相机数据

    Args:
        file_path: 3ds.ms文件路径

    Returns:
        包含相机数据的字典
    """
    camera_data = {"frames": [], "camera_info": {}}

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 提取相机基本信息
    # 提取焦距 - 从newCam行提取 focal:21.446411132812486
    focal_match = re.search(r"newCam = freecamera.*?focal:([\d\.-]+)", content)
    if focal_match:
        camera_data["camera_info"]["focal_length"] = float(focal_match.group(1))

    # 提取渲染分辨率
    width_match = re.search(r"renderWidth = (\d+)", content)
    height_match = re.search(r"renderHeight = (\d+)", content)
    if width_match:
        camera_data["camera_info"]["render_width"] = int(width_match.group(1))
    if height_match:
        camera_data["camera_info"]["render_height"] = int(height_match.group(1))

    # 提取渲染光圈宽度
    aperture_match = re.search(r"setRendApertureWidth ([\d\.-]+)", content)
    if aperture_match:
        camera_data["camera_info"]["aperture_width"] = float(aperture_match.group(1))

    # 提取动画范围
    range_match = re.search(r"animationRange = interval (\d+)f (\d+)f", content)
    if range_match:
        camera_data["camera_info"]["frame_start"] = int(range_match.group(1))
        camera_data["camera_info"]["frame_end"] = int(range_match.group(2))

    # 提取关键帧数据
    frames_dict = {}

    # 提取位置关键帧 key_pos.value = [x,y,z]
    pos_pattern = r"key_pos = addNewKey ctrl_pos (\d+)f\s*\n\s*key_pos\.value = \[([-\d\.]+),([-\d\.]+),([-\d\.]+)\]"
    pos_matches = re.findall(pos_pattern, content)

    for match in pos_matches:
        frame, x, y, z = match
        frame_num = int(frame)
        if frame_num not in frames_dict:
            frames_dict[frame_num] = {"frame_id": frame_num}
        frames_dict[frame_num]["location"] = [float(x), float(y), float(z)]

    # 提取旋转关键帧 - 使用更精确的模式
    rotation_block_pattern = r"key_rotx = addNewKey xrotctrl (\d+)f\s*\n\s*key_roty = addNewKey yrotctrl \d+f\s*\n\s*key_rotz = addNewKey zrotctrl \d+f\s*\n\s*key_rotx\.value = ([-\d\.]+)\s*\n\s*key_roty\.value = ([-\d\.]+)\s*\n\s*key_rotz\.value = ([-\d\.]+)"

    rotation_matches = re.findall(rotation_block_pattern, content, re.MULTILINE)

    for match in rotation_matches:
        frame, rot_x, rot_y, rot_z = match
        frame_num = int(frame)
        if frame_num not in frames_dict:
            frames_dict[frame_num] = {"frame_id": frame_num}

        # 将度转换为弧度
        frames_dict[frame_num]["rotation_euler"] = [
            math.radians(float(rot_x)),
            math.radians(float(rot_y)),
            math.radians(float(rot_z)),
        ]

    # 转换为数组格式（按帧号排序）
    camera_data["frames"] = []
    for frame_num in sorted(frames_dict.keys()):
        camera_data["frames"].append(frames_dict[frame_num])

    return camera_data


def extract_point_data(file_path: Path) -> List[Dict[str, Any]]:
    """
    从3ds.ms文件中提取点位置数据

    Args:
        file_path: 3ds.ms文件路径

    Returns:
        包含点数据的列表，每个点包含point_id和location
    """
    points_data = []

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 提取点数据 - 匹配 point name:"pa0473" position:[x,y,z] parent: pgHelper
    point_pattern = r'point name:"([^"]+)" position:\[([-\d\.]+),([-\d\.]+),([-\d\.]+)\] parent: pgHelper'
    point_matches = re.findall(point_pattern, content)

    for match in point_matches:
        name, x, y, z = match
        point_data = {"point_id": name, "location": [float(x), float(y), float(z)]}
        points_data.append(point_data)

    return points_data


def load_video_data(file_path: Path) -> Dict[str, Any]:
    """
    加载视频数据文件

    Args:
        file_path: video_data.json文件路径

    Returns:
        视频数据字典，如果文件不存在则返回空字典
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"警告：找不到视频数据文件 {file_path}，将使用空的视频数据")
        return {}


def euler_to_rotation_matrix(euler_angles) -> np.ndarray:
    """
    将欧拉角转换为旋转矩阵 (ZYX顺序)

    Args:
        euler_angles: [x, y, z] 欧拉角 (弧度)

    Returns:
        3x3旋转矩阵
    """
    x, y, z = euler_angles

    # 创建各轴旋转矩阵
    Rx = np.array(
        [[1, 0, 0], [0, math.cos(x), -math.sin(x)], [0, math.sin(x), math.cos(x)]]
    )

    Ry = np.array(
        [[math.cos(y), 0, math.sin(y)], [0, 1, 0], [-math.sin(y), 0, math.cos(y)]]
    )

    Rz = np.array(
        [[math.cos(z), -math.sin(z), 0], [math.sin(z), math.cos(z), 0], [0, 0, 1]]
    )

    # ZYX顺序：R = Rz * Ry * Rx
    return Rz @ Ry @ Rx


def rotation_matrix_to_euler(rotation_matrix: np.ndarray) -> List[float]:
    """
    将旋转矩阵转换为欧拉角 (ZYX顺序)

    Args:
        rotation_matrix: 3x3旋转矩阵

    Returns:
        [x, y, z] 欧拉角 (弧度)
    """
    # 提取旋转矩阵元素
    r11, r12, r13 = rotation_matrix[0, :]
    r21, r22, r23 = rotation_matrix[1, :]
    r31, r32, r33 = rotation_matrix[2, :]

    # 计算欧拉角 (ZYX顺序)
    # 检查万向锁情况
    if abs(r31) >= 1.0:
        # 万向锁情况
        z = 0  # 可以设置为任意值
        if r31 < 0:
            y = math.pi / 2
            x = z + math.atan2(r12, r13)
        else:
            y = -math.pi / 2
            x = -z + math.atan2(-r12, -r13)
    else:
        # 正常情况
        y = -math.asin(r31)
        x = math.atan2(r32, r33)
        z = math.atan2(r21, r11)

    return [x, y, z]


def transform_point_relative_to_first(
    first_frame: Dict[str, Any], point_location: List[float]
) -> List[float]:
    """
    将点位置转换为相对于第一帧的坐标系

    Args:
        first_frame: 第一帧数据
        point_location: 点的世界坐标位置

    Returns:
        转换后的相对位置
    """
    # 获取第一帧的位置和旋转
    first_location = np.array(first_frame["location"])
    first_rotation = np.array(first_frame["rotation_euler"])

    # 计算第一帧的旋转矩阵
    first_rotation_matrix = euler_to_rotation_matrix(first_rotation)

    # 计算相对位置：将点位置减去第一帧位置，然后应用第一帧旋转的逆变换
    point_array = np.array(point_location)
    relative_location = point_array - first_location
    # 应用第一帧旋转的逆变换（转置）
    relative_location = first_rotation_matrix.T @ relative_location

    return relative_location.tolist()


def transform_frame_relative_to_first(
    first_frame: Dict[str, Any], current_frame: Dict[str, Any]
) -> Dict[str, Any]:
    """
    将当前帧转换为相对于第一帧的坐标系

    Args:
        first_frame: 第一帧数据
        current_frame: 当前帧数据

    Returns:
        转换后的帧数据
    """
    # 获取第一帧的位置和旋转
    first_location = np.array(first_frame["location"])
    first_rotation = np.array(first_frame["rotation_euler"])

    # 获取当前帧的位置和旋转
    current_location = np.array(current_frame["location"])
    current_rotation = np.array(current_frame["rotation_euler"])

    # 计算第一帧的旋转矩阵
    first_rotation_matrix = euler_to_rotation_matrix(first_rotation)

    # 计算相对位置：将当前位置减去第一帧位置，然后应用第一帧旋转的逆变换
    relative_location = current_location - first_location
    # 应用第一帧旋转的逆变换（转置）
    relative_location = first_rotation_matrix.T @ relative_location

    # 计算相对旋转：当前旋转相对于第一帧旋转
    current_rotation_matrix = euler_to_rotation_matrix(current_rotation)
    # 相对旋转矩阵 = 第一帧旋转的逆 * 当前旋转
    relative_rotation_matrix = first_rotation_matrix.T @ current_rotation_matrix

    # 转换回欧拉角
    relative_rotation = rotation_matrix_to_euler(relative_rotation_matrix)

    # 创建新的帧数据
    transformed_frame = current_frame.copy()
    transformed_frame["location"] = relative_location.tolist()
    transformed_frame["rotation_euler"] = relative_rotation

    return transformed_frame


def convert_video_data_to_relative(
    video_data: Dict[str, Any], first_frame: Dict[str, Any]
) -> Dict[str, Any]:
    """
    转换视频数据为相对坐标系

    Args:
        video_data: 视频数据字典
        first_frame: 3ds数据的第一帧，用作参考

    Returns:
        转换后的视频数据
    """
    if not video_data or "plane" not in video_data:
        return video_data

    converted_video_data = video_data.copy()

    if "position" in video_data["plane"]:
        print("正在转换视频平面位置...")

        # 转换position
        plane_position = video_data["plane"]["position"]
        transformed_position = transform_point_relative_to_first(
            first_frame, plane_position
        )
        converted_video_data["plane"]["position"] = transformed_position

        # 转换rotation（从度数转换为弧度，然后进行相对转换，再转回度数）
        if "rotation" in video_data["plane"]:
            plane_rotation_degrees = video_data["plane"]["rotation"]
            # 转换为弧度
            plane_rotation_radians = [
                math.radians(deg) for deg in plane_rotation_degrees
            ]

            # 创建临时帧数据进行旋转转换
            temp_frame = {
                "location": plane_position,
                "rotation_euler": plane_rotation_radians,
            }

            # 进行相对转换
            transformed_temp_frame = transform_frame_relative_to_first(
                first_frame, temp_frame
            )

            # 转换回度数
            transformed_rotation_degrees = [
                math.degrees(rad) for rad in transformed_temp_frame["rotation_euler"]
            ]
            converted_video_data["plane"]["rotation"] = transformed_rotation_degrees

            print(f"原始视频平面位置: {plane_position}")
            print(f"转换后视频平面位置: {transformed_position}")
            print(f"原始视频平面旋转(度): {plane_rotation_degrees}")
            print(f"转换后视频平面旋转(度): {transformed_rotation_degrees}")

    return converted_video_data


def main():
    """主函数"""
    # 输入文件路径
    # root_path = Path("D:/HuyaProject2/slam_greenscreen/data/new_len/cyber_len")
    root_path = Path("D:/HuyaProject2/slam_greenscreen/data/cyber_len2")
    # root_path = Path("D:/HuyaProject2/slam_greenscreen/data/bridge")
    # root_path = Path("D:/HuyaProject2/slam_greenscreen/data/girl_river1")
    input_3ds_file = root_path / "1.ms"
    input_video_file = root_path / "plane.json"

    # 输出文件路径 - 合并后的统一JSON文件
    output_file = root_path / "output_cameras.json"

    # 检查输入文件是否存在
    if not os.path.exists(input_3ds_file):
        print(f"错误：找不到输入文件 {input_3ds_file}")
        return

    print("开始提取和转换3DS Max数据...")

    # 1. 提取3DS数据
    print("提取相机数据...")
    camera_data = extract_camera_data(input_3ds_file)
    print(f"提取到 {len(camera_data['frames'])} 帧相机数据")

    print("提取点位置数据...")
    points_data = extract_point_data(input_3ds_file)
    print(f"提取到 {len(points_data)} 个点")

    # 2. 加载视频数据
    print("加载视频数据...")
    video_data = load_video_data(input_video_file)

    # 3. 转换为相对坐标系
    frames = camera_data["frames"]

    if not frames:
        print("错误：没有找到相机帧数据")
        return

    print(f"找到 {len(frames)} 帧数据")

    # 获取第一帧作为参考
    first_frame = frames[0]
    print(f"第一帧位置: {first_frame['location']}")
    print(f"第一帧旋转: {first_frame['rotation_euler']}")

    # 转换所有相机帧
    print("转换相机帧数据...")
    transformed_frames = []

    for i, frame in enumerate(frames):
        if i == 0:
            # 第一帧设置为原点
            transformed_frame = frame.copy()
            transformed_frame["location"] = [0.0, 0.0, 0.0]
            transformed_frame["rotation_euler"] = [0.0, 0.0, 0.0]
        else:
            # 其他帧相对于第一帧进行转换
            transformed_frame = transform_frame_relative_to_first(first_frame, frame)

        transformed_frames.append(transformed_frame)

        if i % 10 == 0:  # 每10帧打印一次进度
            print(f"已处理相机帧 {i+1}/{len(frames)}")

    # 转换所有点的位置
    print(f"转换 {len(points_data)} 个点的位置...")
    transformed_points = []

    for i, point in enumerate(points_data):
        transformed_point = point.copy()
        # 转换点的位置相对于第一帧
        transformed_point["location"] = transform_point_relative_to_first(
            first_frame, point["location"]
        )
        transformed_points.append(transformed_point)

        if i % 50 == 0:  # 每50个点打印一次进度
            print(f"已处理点 {i+1}/{len(points_data)}")

    # 转换视频数据
    print("转换视频数据...")
    transformed_video_data = convert_video_data_to_relative(video_data, first_frame)

    # 4. 构建合并的输出数据结构
    combined_data = {
        "camera": {
            "frames": transformed_frames,
            "camera_info": camera_data["camera_info"],
        },
        "points": transformed_points,
        "video": transformed_video_data,
        "metadata": {
            "coordinate_system": "3DS Max (relative to first frame)",
            "units": "centimeters",
            "angles": "radians for camera, degrees for video",
            "description": "Combined camera, point and video data extracted from 3DS Max export and converted to relative coordinate system",
            "first_frame_reference": {
                "original_location": first_frame["location"],
                "original_rotation": first_frame["rotation_euler"],
            },
        },
    }

    # 5. 保存合并后的JSON文件
    print(f"保存合并数据到 {output_file}...")
    # 确保输出文件夹存在
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出文件夹: {output_dir}")
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(combined_data, f, indent=2, ensure_ascii=False)

    print("数据提取和转换完成！")
    print(f"相机帧数: {len(transformed_frames)}")
    print(f"点数量: {len(transformed_points)}")
    print(f"视频数据: {'已包含' if transformed_video_data else '未找到'}")
    print(f"输出文件: {output_file}")
    print(f"第一帧已设置为原点 [0, 0, 0] 和零旋转 [0, 0, 0]")
    print(f"所有后续帧和点都相对于第一帧进行了转换")

    # 显示一些示例数据
    if transformed_frames:
        first_frame_data = transformed_frames[0]
        print(f"\n示例 - 第{first_frame_data['frame_id']}帧相机数据:")
        print(f"  位置: {first_frame_data['location']}")
        print(f"  旋转(弧度): {first_frame_data['rotation_euler']}")

    # 显示相机信息
    if camera_data["camera_info"]:
        print(f"\n相机信息:")
        for key, value in camera_data["camera_info"].items():
            print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
