#version 310 es

precision mediump float;

layout(location=0) in vec4 color;
layout(location=1) in float objectType;
layout(location=2) in vec3 worldPos;
layout(location=3) in vec2 texCoord;

layout(binding=0) uniform FragUniforms {
    vec3 gridColor;
    vec3 baseColor;
    float gridSize;
    float lineWidth;
};

layout(binding=0) uniform sampler2D FG_TEX;

layout(location=0) out vec4 gl_FragColor;

void main()
{
    if (objectType < 1.5) {
        // 地面网格渲染
        vec2 grid = abs(fract(worldPos.xz / gridSize - 0.5) - 0.5) / fwidth(worldPos.xz / gridSize);
        float line = min(grid.x, grid.y);

        float gridStrength = 1.0 - min(line, 1.0);
        vec3 finalColor = mix(baseColor, gridColor, gridStrength);

        gl_FragColor = vec4(finalColor, 1.0);
    } else if (objectType < 2.5) {
        // 坐标轴渲染 - 使用顶点颜色（红色X轴，绿色Y轴，蓝色Z轴）
        gl_FragColor = color;
    } else if (objectType < 3.5) {
        // 关键点渲染 - 黄色
        gl_FragColor = vec4(1.0, 1.0, 0.0, 1.0);
    } else if (objectType < 4.5) {
        // 前景视频面片渲染 - 使用FG_TEX纹理
        gl_FragColor = texture(FG_TEX, texCoord);
    } else {
        // 相机位置渲染 - 使用顶点颜色
        gl_FragColor = color;
    }
}
